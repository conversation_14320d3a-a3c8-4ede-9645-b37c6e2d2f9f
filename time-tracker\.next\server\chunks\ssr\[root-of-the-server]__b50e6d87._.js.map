{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Doug/New%20folder/time-tracker/src/lib/storage.ts"], "sourcesContent": ["import { Order, AppState } from '@/types';\n\nconst STORAGE_KEY = 'time-tracker-data';\n\nexport const loadAppState = (): AppState => {\n  if (typeof window === 'undefined') {\n    return { orders: [] };\n  }\n  \n  try {\n    const stored = localStorage.getItem(STORAGE_KEY);\n    if (stored) {\n      return JSON.parse(stored);\n    }\n  } catch (error) {\n    console.error('Error loading app state:', error);\n  }\n  \n  return { orders: [] };\n};\n\nexport const saveAppState = (state: AppState): void => {\n  if (typeof window === 'undefined') return;\n  \n  try {\n    localStorage.setItem(STORAGE_KEY, JSON.stringify(state));\n  } catch (error) {\n    console.error('Error saving app state:', error);\n  }\n};\n\nexport const saveOrder = (order: Order): void => {\n  const state = loadAppState();\n  const existingIndex = state.orders.findIndex(o => o.id === order.id);\n  \n  if (existingIndex >= 0) {\n    state.orders[existingIndex] = order;\n  } else {\n    state.orders.push(order);\n  }\n  \n  saveAppState(state);\n};\n\nexport const deleteOrder = (orderId: string): void => {\n  const state = loadAppState();\n  state.orders = state.orders.filter(o => o.id !== orderId);\n  saveAppState(state);\n};\n\nexport const getOrder = (orderId: string): Order | undefined => {\n  const state = loadAppState();\n  return state.orders.find(o => o.id === orderId);\n};\n"], "names": [], "mappings": ";;;;;;;AAEA,MAAM,cAAc;AAEb,MAAM,eAAe;IAC1B,wCAAmC;QACjC,OAAO;YAAE,QAAQ,EAAE;QAAC;IACtB;;AAYF;AAEO,MAAM,eAAe,CAAC;IAC3B,wCAAmC;;AAOrC;AAEO,MAAM,YAAY,CAAC;IACxB,MAAM,QAAQ;IACd,MAAM,gBAAgB,MAAM,MAAM,CAAC,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,MAAM,EAAE;IAEnE,IAAI,iBAAiB,GAAG;QACtB,MAAM,MAAM,CAAC,cAAc,GAAG;IAChC,OAAO;QACL,MAAM,MAAM,CAAC,IAAI,CAAC;IACpB;IAEA,aAAa;AACf;AAEO,MAAM,cAAc,CAAC;IAC1B,MAAM,QAAQ;IACd,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;IACjD,aAAa;AACf;AAEO,MAAM,WAAW,CAAC;IACvB,MAAM,QAAQ;IACd,OAAO,MAAM,MAAM,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;AACzC", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Doug/New%20folder/time-tracker/src/components/TimeTracker.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Play, Pause, Square } from 'lucide-react';\nimport { Order, TimeEntry } from '@/types';\nimport { formatTime, calculateTimeDifference } from '@/lib/utils';\nimport { saveOrder } from '@/lib/storage';\nimport { v4 as uuidv4 } from 'uuid';\n\ninterface TimeTrackerProps {\n  order: Order;\n  onOrderUpdate: (order: Order) => void;\n}\n\nconst TimeTracker = ({ order, onOrderUpdate }: TimeTrackerProps) => {\n  const [currentTime, setCurrentTime] = useState(0);\n  const [description, setDescription] = useState('');\n  \n  const activeEntry = order.timeEntries.find(entry => entry.isActive);\n  const isTracking = !!activeEntry;\n\n  useEffect(() => {\n    let interval: NodeJS.Timeout;\n    \n    if (isTracking && activeEntry) {\n      interval = setInterval(() => {\n        const elapsed = calculateTimeDifference(activeEntry.startTime);\n        setCurrentTime(elapsed);\n      }, 1000);\n    }\n    \n    return () => {\n      if (interval) clearInterval(interval);\n    };\n  }, [isTracking, activeEntry]);\n\n  const startTimer = () => {\n    const newEntry: TimeEntry = {\n      id: uuidv4(),\n      orderId: order.id,\n      startTime: new Date().toISOString(),\n      description: description.trim() || undefined,\n      isActive: true,\n    };\n\n    const updatedOrder = {\n      ...order,\n      timeEntries: [...order.timeEntries, newEntry],\n      status: order.status === 'pending' ? 'in-progress' as const : order.status,\n      updatedAt: new Date().toISOString(),\n    };\n\n    saveOrder(updatedOrder);\n    onOrderUpdate(updatedOrder);\n    setDescription('');\n  };\n\n  const stopTimer = () => {\n    if (!activeEntry) return;\n\n    const endTime = new Date().toISOString();\n    const duration = calculateTimeDifference(activeEntry.startTime, endTime);\n\n    const updatedTimeEntries = order.timeEntries.map(entry =>\n      entry.id === activeEntry.id\n        ? { ...entry, endTime, isActive: false }\n        : entry\n    );\n\n    const totalTime = order.totalTimeSpent + duration;\n\n    const updatedOrder = {\n      ...order,\n      timeEntries: updatedTimeEntries,\n      totalTimeSpent: totalTime,\n      updatedAt: new Date().toISOString(),\n    };\n\n    saveOrder(updatedOrder);\n    onOrderUpdate(updatedOrder);\n    setCurrentTime(0);\n  };\n\n  return (\n    <div className=\"bg-white rounded-lg shadow-sm border p-4 space-y-4\">\n      <div className=\"flex items-center justify-between\">\n        <h3 className=\"text-lg font-semibold text-gray-900\">Time Tracker</h3>\n        <div className=\"text-2xl font-mono font-bold text-blue-600\">\n          {isTracking ? formatTime(currentTime) : formatTime(order.totalTimeSpent)}\n        </div>\n      </div>\n\n      {!isTracking && (\n        <div className=\"space-y-3\">\n          <textarea\n            value={description}\n            onChange={(e) => setDescription(e.target.value)}\n            placeholder=\"What are you working on? (optional)\"\n            className=\"w-full p-3 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            rows={2}\n          />\n        </div>\n      )}\n\n      <div className=\"flex gap-2\">\n        {!isTracking ? (\n          <button\n            onClick={startTimer}\n            className=\"flex-1 flex items-center justify-center gap-2 bg-green-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-green-700 transition-colors\"\n          >\n            <Play size={20} />\n            Start Timer\n          </button>\n        ) : (\n          <button\n            onClick={stopTimer}\n            className=\"flex-1 flex items-center justify-center gap-2 bg-red-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-red-700 transition-colors\"\n          >\n            <Square size={20} />\n            Stop Timer\n          </button>\n        )}\n      </div>\n\n      {activeEntry && (\n        <div className=\"bg-blue-50 p-3 rounded-lg\">\n          <p className=\"text-sm text-blue-800\">\n            <span className=\"font-medium\">Started:</span> {new Date(activeEntry.startTime).toLocaleTimeString()}\n          </p>\n          {activeEntry.description && (\n            <p className=\"text-sm text-blue-800 mt-1\">\n              <span className=\"font-medium\">Task:</span> {activeEntry.description}\n            </p>\n          )}\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default TimeTracker;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAEA;AACA;AACA;AAPA;;;;;;;AAcA,MAAM,cAAc,CAAC,EAAE,KAAK,EAAE,aAAa,EAAoB;IAC7D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,cAAc,MAAM,WAAW,CAAC,IAAI,CAAC,CAAA,QAAS,MAAM,QAAQ;IAClE,MAAM,aAAa,CAAC,CAAC;IAErB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI;QAEJ,IAAI,cAAc,aAAa;YAC7B,WAAW,YAAY;gBACrB,MAAM,UAAU,CAAA,GAAA,mHAAA,CAAA,0BAAuB,AAAD,EAAE,YAAY,SAAS;gBAC7D,eAAe;YACjB,GAAG;QACL;QAEA,OAAO;YACL,IAAI,UAAU,cAAc;QAC9B;IACF,GAAG;QAAC;QAAY;KAAY;IAE5B,MAAM,aAAa;QACjB,MAAM,WAAsB;YAC1B,IAAI,CAAA,GAAA,0KAAA,CAAA,KAAM,AAAD;YACT,SAAS,MAAM,EAAE;YACjB,WAAW,IAAI,OAAO,WAAW;YACjC,aAAa,YAAY,IAAI,MAAM;YACnC,UAAU;QACZ;QAEA,MAAM,eAAe;YACnB,GAAG,KAAK;YACR,aAAa;mBAAI,MAAM,WAAW;gBAAE;aAAS;YAC7C,QAAQ,MAAM,MAAM,KAAK,YAAY,gBAAyB,MAAM,MAAM;YAC1E,WAAW,IAAI,OAAO,WAAW;QACnC;QAEA,CAAA,GAAA,qHAAA,CAAA,YAAS,AAAD,EAAE;QACV,cAAc;QACd,eAAe;IACjB;IAEA,MAAM,YAAY;QAChB,IAAI,CAAC,aAAa;QAElB,MAAM,UAAU,IAAI,OAAO,WAAW;QACtC,MAAM,WAAW,CAAA,GAAA,mHAAA,CAAA,0BAAuB,AAAD,EAAE,YAAY,SAAS,EAAE;QAEhE,MAAM,qBAAqB,MAAM,WAAW,CAAC,GAAG,CAAC,CAAA,QAC/C,MAAM,EAAE,KAAK,YAAY,EAAE,GACvB;gBAAE,GAAG,KAAK;gBAAE;gBAAS,UAAU;YAAM,IACrC;QAGN,MAAM,YAAY,MAAM,cAAc,GAAG;QAEzC,MAAM,eAAe;YACnB,GAAG,KAAK;YACR,aAAa;YACb,gBAAgB;YAChB,WAAW,IAAI,OAAO,WAAW;QACnC;QAEA,CAAA,GAAA,qHAAA,CAAA,YAAS,AAAD,EAAE;QACV,cAAc;QACd,eAAe;IACjB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAsC;;;;;;kCACpD,8OAAC;wBAAI,WAAU;kCACZ,aAAa,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD,EAAE,eAAe,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD,EAAE,MAAM,cAAc;;;;;;;;;;;;YAI1E,CAAC,4BACA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBACC,OAAO;oBACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;oBAC9C,aAAY;oBACZ,WAAU;oBACV,MAAM;;;;;;;;;;;0BAKZ,8OAAC;gBAAI,WAAU;0BACZ,CAAC,2BACA,8OAAC;oBACC,SAAS;oBACT,WAAU;;sCAEV,8OAAC,kMAAA,CAAA,OAAI;4BAAC,MAAM;;;;;;wBAAM;;;;;;yCAIpB,8OAAC;oBACC,SAAS;oBACT,WAAU;;sCAEV,8OAAC,sMAAA,CAAA,SAAM;4BAAC,MAAM;;;;;;wBAAM;;;;;;;;;;;;YAMzB,6BACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAE,WAAU;;0CACX,8OAAC;gCAAK,WAAU;0CAAc;;;;;;4BAAe;4BAAE,IAAI,KAAK,YAAY,SAAS,EAAE,kBAAkB;;;;;;;oBAElG,YAAY,WAAW,kBACtB,8OAAC;wBAAE,WAAU;;0CACX,8OAAC;gCAAK,WAAU;0CAAc;;;;;;4BAAY;4BAAE,YAAY,WAAW;;;;;;;;;;;;;;;;;;;AAOjF;uCAEe", "debugId": null}}, {"offset": {"line": 287, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Doug/New%20folder/time-tracker/src/app/orders/%5Bid%5D/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useParams, useRouter } from 'next/navigation';\nimport Link from 'next/link';\nimport { ArrowLeft, Edit, User, Package, Clock, Calendar, AlertCircle } from 'lucide-react';\nimport { Order } from '@/types';\nimport { getOrder } from '@/lib/storage';\nimport { formatTime, formatDate, formatDateTime, getStatusColor, getPriorityColor, cn } from '@/lib/utils';\nimport TimeTracker from '@/components/TimeTracker';\n\nexport default function OrderDetailsPage() {\n  const params = useParams();\n  const router = useRouter();\n  const [order, setOrder] = useState<Order | null>(null);\n  const [loading, setLoading] = useState(true);\n\n  const orderId = params.id as string;\n\n  useEffect(() => {\n    if (orderId) {\n      const foundOrder = getOrder(orderId);\n      if (foundOrder) {\n        setOrder(foundOrder);\n      } else {\n        router.push('/orders');\n      }\n      setLoading(false);\n    }\n  }, [orderId, router]);\n\n  const handleOrderUpdate = (updatedOrder: Order) => {\n    setOrder(updatedOrder);\n  };\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center min-h-screen\">\n        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  if (!order) {\n    return (\n      <div className=\"max-w-md mx-auto p-4 text-center\">\n        <h1 className=\"text-xl font-bold text-gray-900\">Order not found</h1>\n        <Link href=\"/orders\" className=\"text-blue-600 hover:underline\">\n          Back to orders\n        </Link>\n      </div>\n    );\n  }\n\n  const partsNeeded = order.parts.filter(part => part.quantity < part.quantityNeeded);\n  const completedTimeEntries = order.timeEntries.filter(entry => !entry.isActive);\n\n  return (\n    <div className=\"max-w-md mx-auto p-4 space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center gap-3\">\n        <Link href=\"/orders\" className=\"p-2 hover:bg-gray-100 rounded-lg\">\n          <ArrowLeft size={20} />\n        </Link>\n        <div className=\"flex-1 min-w-0\">\n          <h1 className=\"text-xl font-bold text-gray-900 truncate\">{order.title}</h1>\n          <p className=\"text-sm text-gray-600\">Order Details</p>\n        </div>\n        <button className=\"p-2 hover:bg-gray-100 rounded-lg\">\n          <Edit size={20} className=\"text-gray-600\" />\n        </button>\n      </div>\n\n      {/* Time Tracker */}\n      <TimeTracker order={order} onOrderUpdate={handleOrderUpdate} />\n\n      {/* Order Info */}\n      <div className=\"bg-white rounded-lg shadow-sm border p-4 space-y-4\">\n        <div className=\"flex items-center justify-between\">\n          <h2 className=\"text-lg font-semibold text-gray-900\">Order Information</h2>\n          <div className=\"flex gap-2\">\n            <span className={cn(\n              \"px-2 py-1 rounded-full text-xs font-medium\",\n              getStatusColor(order.status)\n            )}>\n              {order.status.replace('-', ' ').toUpperCase()}\n            </span>\n            <span className={cn(\n              \"px-2 py-1 rounded-full text-xs font-medium\",\n              getPriorityColor(order.priority)\n            )}>\n              {order.priority.toUpperCase()}\n            </span>\n          </div>\n        </div>\n\n        {order.description && (\n          <p className=\"text-gray-700\">{order.description}</p>\n        )}\n\n        <div className=\"grid grid-cols-2 gap-4 text-sm\">\n          <div>\n            <p className=\"text-gray-600\">Created</p>\n            <p className=\"font-medium\">{formatDate(order.createdAt)}</p>\n          </div>\n          <div>\n            <p className=\"text-gray-600\">Updated</p>\n            <p className=\"font-medium\">{formatDate(order.updatedAt)}</p>\n          </div>\n          {order.estimatedDelivery && (\n            <div className=\"col-span-2\">\n              <p className=\"text-gray-600\">Estimated Delivery</p>\n              <p className=\"font-medium\">{formatDate(order.estimatedDelivery)}</p>\n            </div>\n          )}\n        </div>\n\n        {order.notes && (\n          <div>\n            <p className=\"text-sm text-gray-600 mb-1\">Notes</p>\n            <p className=\"text-gray-700 bg-gray-50 p-2 rounded\">{order.notes}</p>\n          </div>\n        )}\n      </div>\n\n      {/* Client Info */}\n      <div className=\"bg-white rounded-lg shadow-sm border p-4 space-y-3\">\n        <div className=\"flex items-center gap-2\">\n          <User size={20} className=\"text-gray-600\" />\n          <h2 className=\"text-lg font-semibold text-gray-900\">Client</h2>\n        </div>\n        \n        <div className=\"space-y-2\">\n          <div>\n            <p className=\"font-medium text-gray-900\">{order.client.name}</p>\n            {order.client.company && (\n              <p className=\"text-sm text-gray-600\">{order.client.company}</p>\n            )}\n          </div>\n          \n          {order.client.email && (\n            <div>\n              <p className=\"text-sm text-gray-600\">Email</p>\n              <a href={`mailto:${order.client.email}`} className=\"text-blue-600 hover:underline\">\n                {order.client.email}\n              </a>\n            </div>\n          )}\n          \n          {order.client.phone && (\n            <div>\n              <p className=\"text-sm text-gray-600\">Phone</p>\n              <a href={`tel:${order.client.phone}`} className=\"text-blue-600 hover:underline\">\n                {order.client.phone}\n              </a>\n            </div>\n          )}\n          \n          {order.client.address && (\n            <div>\n              <p className=\"text-sm text-gray-600\">Address</p>\n              <p className=\"text-gray-700\">{order.client.address}</p>\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* Parts */}\n      {order.parts.length > 0 && (\n        <div className=\"bg-white rounded-lg shadow-sm border p-4 space-y-4\">\n          <div className=\"flex items-center gap-2\">\n            <Package size={20} className=\"text-gray-600\" />\n            <h2 className=\"text-lg font-semibold text-gray-900\">Parts & Materials</h2>\n          </div>\n\n          {partsNeeded.length > 0 && (\n            <div className=\"bg-amber-50 border border-amber-200 rounded-lg p-3\">\n              <div className=\"flex items-center gap-2\">\n                <AlertCircle size={16} className=\"text-amber-600\" />\n                <p className=\"text-sm font-medium text-amber-800\">\n                  {partsNeeded.length} part{partsNeeded.length !== 1 ? 's' : ''} needed\n                </p>\n              </div>\n            </div>\n          )}\n\n          <div className=\"space-y-3\">\n            {order.parts.map(part => {\n              const isNeeded = part.quantity < part.quantityNeeded;\n              return (\n                <div key={part.id} className={cn(\n                  \"border rounded-lg p-3 space-y-2\",\n                  isNeeded ? \"border-amber-200 bg-amber-50\" : \"border-gray-200\"\n                )}>\n                  <div className=\"flex items-center justify-between\">\n                    <h3 className=\"font-medium text-gray-900\">{part.name}</h3>\n                    <span className={cn(\n                      \"text-sm font-medium\",\n                      isNeeded ? \"text-amber-700\" : \"text-green-700\"\n                    )}>\n                      {part.quantity}/{part.quantityNeeded}\n                    </span>\n                  </div>\n                  \n                  {part.supplier && (\n                    <p className=\"text-sm text-gray-600\">Supplier: {part.supplier}</p>\n                  )}\n                  \n                  {part.expectedDelivery && (\n                    <p className=\"text-sm text-gray-600\">\n                      Expected: {formatDate(part.expectedDelivery)}\n                    </p>\n                  )}\n                  \n                  {part.notes && (\n                    <p className=\"text-sm text-gray-700 bg-gray-50 p-2 rounded\">{part.notes}</p>\n                  )}\n                </div>\n              );\n            })}\n          </div>\n        </div>\n      )}\n\n      {/* Time Entries */}\n      {completedTimeEntries.length > 0 && (\n        <div className=\"bg-white rounded-lg shadow-sm border p-4 space-y-4\">\n          <div className=\"flex items-center gap-2\">\n            <Clock size={20} className=\"text-gray-600\" />\n            <h2 className=\"text-lg font-semibold text-gray-900\">Time History</h2>\n          </div>\n          \n          <div className=\"space-y-2\">\n            {completedTimeEntries\n              .sort((a, b) => new Date(b.startTime).getTime() - new Date(a.startTime).getTime())\n              .map(entry => (\n                <div key={entry.id} className=\"border border-gray-200 rounded-lg p-3\">\n                  <div className=\"flex items-center justify-between\">\n                    <span className=\"font-medium text-gray-900\">\n                      {formatTime(\n                        Math.floor((new Date(entry.endTime!).getTime() - new Date(entry.startTime).getTime()) / (1000 * 60))\n                      )}\n                    </span>\n                    <span className=\"text-sm text-gray-600\">\n                      {formatDateTime(entry.startTime)}\n                    </span>\n                  </div>\n                  {entry.description && (\n                    <p className=\"text-sm text-gray-700 mt-1\">{entry.description}</p>\n                  )}\n                </div>\n              ))}\n          </div>\n          \n          <div className=\"border-t pt-3\">\n            <div className=\"flex items-center justify-between\">\n              <span className=\"font-medium text-gray-900\">Total Time</span>\n              <span className=\"text-lg font-bold text-blue-600\">\n                {formatTime(order.totalTimeSpent)}\n              </span>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;AACA;AACA;AATA;;;;;;;;;AAWe,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB;IACjD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,UAAU,OAAO,EAAE;IAEzB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,SAAS;YACX,MAAM,aAAa,CAAA,GAAA,qHAAA,CAAA,WAAQ,AAAD,EAAE;YAC5B,IAAI,YAAY;gBACd,SAAS;YACX,OAAO;gBACL,OAAO,IAAI,CAAC;YACd;YACA,WAAW;QACb;IACF,GAAG;QAAC;QAAS;KAAO;IAEpB,MAAM,oBAAoB,CAAC;QACzB,SAAS;IACX;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,IAAI,CAAC,OAAO;QACV,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;8BAAkC;;;;;;8BAChD,8OAAC,4JAAA,CAAA,UAAI;oBAAC,MAAK;oBAAU,WAAU;8BAAgC;;;;;;;;;;;;IAKrE;IAEA,MAAM,cAAc,MAAM,KAAK,CAAC,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,GAAG,KAAK,cAAc;IAClF,MAAM,uBAAuB,MAAM,WAAW,CAAC,MAAM,CAAC,CAAA,QAAS,CAAC,MAAM,QAAQ;IAE9E,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,4JAAA,CAAA,UAAI;wBAAC,MAAK;wBAAU,WAAU;kCAC7B,cAAA,8OAAC,gNAAA,CAAA,YAAS;4BAAC,MAAM;;;;;;;;;;;kCAEnB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA4C,MAAM,KAAK;;;;;;0CACrE,8OAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;kCAEvC,8OAAC;wBAAO,WAAU;kCAChB,cAAA,8OAAC,2MAAA,CAAA,OAAI;4BAAC,MAAM;4BAAI,WAAU;;;;;;;;;;;;;;;;;0BAK9B,8OAAC,iIAAA,CAAA,UAAW;gBAAC,OAAO;gBAAO,eAAe;;;;;;0BAG1C,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAsC;;;;;;0CACpD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAChB,8CACA,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,MAAM;kDAE1B,MAAM,MAAM,CAAC,OAAO,CAAC,KAAK,KAAK,WAAW;;;;;;kDAE7C,8OAAC;wCAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAChB,8CACA,CAAA,GAAA,mHAAA,CAAA,mBAAgB,AAAD,EAAE,MAAM,QAAQ;kDAE9B,MAAM,QAAQ,CAAC,WAAW;;;;;;;;;;;;;;;;;;oBAKhC,MAAM,WAAW,kBAChB,8OAAC;wBAAE,WAAU;kCAAiB,MAAM,WAAW;;;;;;kCAGjD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAE,WAAU;kDAAgB;;;;;;kDAC7B,8OAAC;wCAAE,WAAU;kDAAe,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD,EAAE,MAAM,SAAS;;;;;;;;;;;;0CAExD,8OAAC;;kDACC,8OAAC;wCAAE,WAAU;kDAAgB;;;;;;kDAC7B,8OAAC;wCAAE,WAAU;kDAAe,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD,EAAE,MAAM,SAAS;;;;;;;;;;;;4BAEvD,MAAM,iBAAiB,kBACtB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;kDAAgB;;;;;;kDAC7B,8OAAC;wCAAE,WAAU;kDAAe,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD,EAAE,MAAM,iBAAiB;;;;;;;;;;;;;;;;;;oBAKnE,MAAM,KAAK,kBACV,8OAAC;;0CACC,8OAAC;gCAAE,WAAU;0CAA6B;;;;;;0CAC1C,8OAAC;gCAAE,WAAU;0CAAwC,MAAM,KAAK;;;;;;;;;;;;;;;;;;0BAMtE,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kMAAA,CAAA,OAAI;gCAAC,MAAM;gCAAI,WAAU;;;;;;0CAC1B,8OAAC;gCAAG,WAAU;0CAAsC;;;;;;;;;;;;kCAGtD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAE,WAAU;kDAA6B,MAAM,MAAM,CAAC,IAAI;;;;;;oCAC1D,MAAM,MAAM,CAAC,OAAO,kBACnB,8OAAC;wCAAE,WAAU;kDAAyB,MAAM,MAAM,CAAC,OAAO;;;;;;;;;;;;4BAI7D,MAAM,MAAM,CAAC,KAAK,kBACjB,8OAAC;;kDACC,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;kDACrC,8OAAC;wCAAE,MAAM,CAAC,OAAO,EAAE,MAAM,MAAM,CAAC,KAAK,EAAE;wCAAE,WAAU;kDAChD,MAAM,MAAM,CAAC,KAAK;;;;;;;;;;;;4BAKxB,MAAM,MAAM,CAAC,KAAK,kBACjB,8OAAC;;kDACC,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;kDACrC,8OAAC;wCAAE,MAAM,CAAC,IAAI,EAAE,MAAM,MAAM,CAAC,KAAK,EAAE;wCAAE,WAAU;kDAC7C,MAAM,MAAM,CAAC,KAAK;;;;;;;;;;;;4BAKxB,MAAM,MAAM,CAAC,OAAO,kBACnB,8OAAC;;kDACC,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;kDACrC,8OAAC;wCAAE,WAAU;kDAAiB,MAAM,MAAM,CAAC,OAAO;;;;;;;;;;;;;;;;;;;;;;;;YAOzD,MAAM,KAAK,CAAC,MAAM,GAAG,mBACpB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,wMAAA,CAAA,UAAO;gCAAC,MAAM;gCAAI,WAAU;;;;;;0CAC7B,8OAAC;gCAAG,WAAU;0CAAsC;;;;;;;;;;;;oBAGrD,YAAY,MAAM,GAAG,mBACpB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,oNAAA,CAAA,cAAW;oCAAC,MAAM;oCAAI,WAAU;;;;;;8CACjC,8OAAC;oCAAE,WAAU;;wCACV,YAAY,MAAM;wCAAC;wCAAM,YAAY,MAAM,KAAK,IAAI,MAAM;wCAAG;;;;;;;;;;;;;;;;;;kCAMtE,8OAAC;wBAAI,WAAU;kCACZ,MAAM,KAAK,CAAC,GAAG,CAAC,CAAA;4BACf,MAAM,WAAW,KAAK,QAAQ,GAAG,KAAK,cAAc;4BACpD,qBACE,8OAAC;gCAAkB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAC7B,mCACA,WAAW,iCAAiC;;kDAE5C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAA6B,KAAK,IAAI;;;;;;0DACpD,8OAAC;gDAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAChB,uBACA,WAAW,mBAAmB;;oDAE7B,KAAK,QAAQ;oDAAC;oDAAE,KAAK,cAAc;;;;;;;;;;;;;oCAIvC,KAAK,QAAQ,kBACZ,8OAAC;wCAAE,WAAU;;4CAAwB;4CAAW,KAAK,QAAQ;;;;;;;oCAG9D,KAAK,gBAAgB,kBACpB,8OAAC;wCAAE,WAAU;;4CAAwB;4CACxB,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD,EAAE,KAAK,gBAAgB;;;;;;;oCAI9C,KAAK,KAAK,kBACT,8OAAC;wCAAE,WAAU;kDAAgD,KAAK,KAAK;;;;;;;+BAzBjE,KAAK,EAAE;;;;;wBA6BrB;;;;;;;;;;;;YAML,qBAAqB,MAAM,GAAG,mBAC7B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,oMAAA,CAAA,QAAK;gCAAC,MAAM;gCAAI,WAAU;;;;;;0CAC3B,8OAAC;gCAAG,WAAU;0CAAsC;;;;;;;;;;;;kCAGtD,8OAAC;wBAAI,WAAU;kCACZ,qBACE,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO,IAC9E,GAAG,CAAC,CAAA,sBACH,8OAAC;gCAAmB,WAAU;;kDAC5B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DACb,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD,EACR,KAAK,KAAK,CAAC,CAAC,IAAI,KAAK,MAAM,OAAO,EAAG,OAAO,KAAK,IAAI,KAAK,MAAM,SAAS,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE;;;;;;0DAGtG,8OAAC;gDAAK,WAAU;0DACb,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,SAAS;;;;;;;;;;;;oCAGlC,MAAM,WAAW,kBAChB,8OAAC;wCAAE,WAAU;kDAA8B,MAAM,WAAW;;;;;;;+BAZtD,MAAM,EAAE;;;;;;;;;;kCAkBxB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,WAAU;8CAA4B;;;;;;8CAC5C,8OAAC;oCAAK,WAAU;8CACb,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD,EAAE,MAAM,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQhD", "debugId": null}}, {"offset": {"line": 1039, "column": 0}, "map": {"version": 3, "file": "arrow-left.js", "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Doug/New%20folder/time-tracker/node_modules/lucide-react/src/icons/arrow-left.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'm12 19-7-7 7-7', key: '1l729n' }],\n  ['path', { d: 'M19 12H5', key: 'x3x0zl' }],\n];\n\n/**\n * @component @name ArrowLeft\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMTIgMTktNy03IDctNyIgLz4KICA8cGF0aCBkPSJNMTkgMTJINSIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/arrow-left\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ArrowLeft = createLucideIcon('arrow-left', __iconNode);\n\nexport default ArrowLeft;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAkB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC/C;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CAC3C;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,SAAA,CAAY,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAc,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1085, "column": 0}, "map": {"version": 3, "file": "square-pen.js", "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Doug/New%20folder/time-tracker/node_modules/lucide-react/src/icons/square-pen.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7', key: '1m0v6g' }],\n  [\n    'path',\n    {\n      d: 'M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z',\n      key: 'ohrbg2',\n    },\n  ],\n];\n\n/**\n * @component @name SquarePen\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIgM0g1YTIgMiAwIDAgMC0yIDJ2MTRhMiAyIDAgMCAwIDIgMmgxNGEyIDIgMCAwIDAgMi0ydi03IiAvPgogIDxwYXRoIGQ9Ik0xOC4zNzUgMi42MjVhMSAxIDAgMCAxIDMgM2wtOS4wMTMgOS4wMTRhMiAyIDAgMCAxLS44NTMuNTA1bC0yLjg3My44NGEuNS41IDAgMCAxLS42Mi0uNjJsLjg0LTIuODczYTIgMiAwIDAgMSAuNTA2LS44NTJ6IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/square-pen\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst SquarePen = createLucideIcon('square-pen', __iconNode);\n\nexport default SquarePen;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA8D,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC3F;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,CAAA,CAAA,CAAK,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACP;KACF;CACF;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,SAAA,CAAY,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAc,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1131, "column": 0}, "map": {"version": 3, "file": "user.js", "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Doug/New%20folder/time-tracker/node_modules/lucide-react/src/icons/user.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2', key: '975kel' }],\n  ['circle', { cx: '12', cy: '7', r: '4', key: '17ys0d' }],\n];\n\n/**\n * @component @name User\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTkgMjF2LTJhNCA0IDAgMCAwLTQtNEg5YTQgNCAwIDAgMC00IDR2MiIgLz4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjciIHI9IjQiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/user\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst User = createLucideIcon('user', __iconNode);\n\nexport default User;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA6C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC1E;QAAC,QAAU,CAAA;QAAA,CAAA;YAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAI,CAAA,CAAA,CAAA,GAAA,CAAK;YAAA,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA;YAAK,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;CACzD;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAO,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1179, "column": 0}, "map": {"version": 3, "file": "package.js", "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Doug/New%20folder/time-tracker/node_modules/lucide-react/src/icons/package.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z',\n      key: '1a0edw',\n    },\n  ],\n  ['path', { d: 'M12 22V12', key: 'd0xqtd' }],\n  ['polyline', { points: '3.29 7 12 12 20.71 7', key: 'ousv84' }],\n  ['path', { d: 'm7.5 4.27 9 5.15', key: '1c824w' }],\n];\n\n/**\n * @component @name Package\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTEgMjEuNzNhMiAyIDAgMCAwIDIgMGw3LTRBMiAyIDAgMCAwIDIxIDE2VjhhMiAyIDAgMCAwLTEtMS43M2wtNy00YTIgMiAwIDAgMC0yIDBsLTcgNEEyIDIgMCAwIDAgMyA4djhhMiAyIDAgMCAwIDEgMS43M3oiIC8+CiAgPHBhdGggZD0iTTEyIDIyVjEyIiAvPgogIDxwb2x5bGluZSBwb2ludHM9IjMuMjkgNyAxMiAxMiAyMC43MSA3IiAvPgogIDxwYXRoIGQ9Im03LjUgNC4yNyA5IDUuMTUiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/package\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Package = createLucideIcon('package', __iconNode);\n\nexport default Package;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,CAAA,CAAA,CAAK,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACP;KACF;IACA;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC1C;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,CAAA;QAAA,CAAA;YAAE,QAAQ,CAAwB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC9D;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAoB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CACnD;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAU,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAW,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1239, "column": 0}, "map": {"version": 3, "file": "circle-alert.js", "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Doug/New%20folder/time-tracker/node_modules/lucide-react/src/icons/circle-alert.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['circle', { cx: '12', cy: '12', r: '10', key: '1mglay' }],\n  ['line', { x1: '12', x2: '12', y1: '8', y2: '12', key: '1pkeuh' }],\n  ['line', { x1: '12', x2: '12.01', y1: '16', y2: '16', key: '4dfq90' }],\n];\n\n/**\n * @component @name CircleAlert\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCIgLz4KICA8bGluZSB4MT0iMTIiIHgyPSIxMiIgeTE9IjgiIHkyPSIxMiIgLz4KICA8bGluZSB4MT0iMTIiIHgyPSIxMi4wMSIgeTE9IjE2IiB5Mj0iMTYiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/circle-alert\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst CircleAlert = createLucideIcon('circle-alert', __iconNode);\n\nexport default CircleAlert;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,QAAU,CAAA;QAAA,CAAA;YAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IACzD;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAE;YAAA,CAAA,CAAA,EAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,EAAA,CAAI,CAAA,CAAA,CAAA,CAAK,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACjE;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAE;YAAA,CAAA,CAAA,EAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,EAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CACvE;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,WAAA,CAAc,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAgB,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1300, "column": 0}, "map": {"version": 3, "file": "play.js", "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Doug/New%20folder/time-tracker/node_modules/lucide-react/src/icons/play.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [['polygon', { points: '6 3 20 12 6 21 6 3', key: '1oa8hb' }]];\n\n/**\n * @component @name Play\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cG9seWdvbiBwb2ludHM9IjYgMyAyMCAxMiA2IDIxIDYgMyIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/play\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Play = createLucideIcon('play', __iconNode);\n\nexport default Play;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,UAAA,CAAuB,CAAA,CAAA;IAAC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW;QAAA,CAAE;YAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAsB;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAC;KAAC;CAAA;AAa3F,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAO,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1339, "column": 0}, "map": {"version": 3, "file": "square.js", "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Doug/New%20folder/time-tracker/node_modules/lucide-react/src/icons/square.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['rect', { width: '18', height: '18', x: '3', y: '3', rx: '2', key: 'afitv7' }],\n];\n\n/**\n * @component @name Square\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iMTgiIGhlaWdodD0iMTgiIHg9IjMiIHk9IjMiIHJ4PSIyIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/square\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Square = createLucideIcon('square', __iconNode);\n\nexport default Square;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ;QAAA,CAAA;YAAE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO;YAAM,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAK,GAAG,CAAK,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAK,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CAChF;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAS,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAU,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1382, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Doug/New%20folder/time-tracker/node_modules/uuid/dist/esm/native.js"], "sourcesContent": ["import { randomUUID } from 'crypto';\nexport default { randomUUID };\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,YAAA,qGAAA,CAAA,aAAU;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1396, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Doug/New%20folder/time-tracker/node_modules/uuid/dist/esm/rng.js"], "sourcesContent": ["import { randomFillSync } from 'crypto';\nconst rnds8Pool = new Uint8Array(256);\nlet poolPtr = rnds8Pool.length;\nexport default function rng() {\n    if (poolPtr > rnds8Pool.length - 16) {\n        randomFillSync(rnds8Pool);\n        poolPtr = 0;\n    }\n    return rnds8Pool.slice(poolPtr, (poolPtr += 16));\n}\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,YAAY,IAAI,WAAW;AACjC,IAAI,UAAU,UAAU,MAAM;AACf,SAAS;IACpB,IAAI,UAAU,UAAU,MAAM,GAAG,IAAI;QACjC,CAAA,GAAA,qGAAA,CAAA,iBAAc,AAAD,EAAE;QACf,UAAU;IACd;IACA,OAAO,UAAU,KAAK,CAAC,SAAU,WAAW;AAChD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1416, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Doug/New%20folder/time-tracker/node_modules/uuid/dist/esm/regex.js"], "sourcesContent": ["export default /^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-8][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000|ffffffff-ffff-ffff-ffff-ffffffffffff)$/i;\n"], "names": [], "mappings": ";;;uCAAe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1426, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Doug/New%20folder/time-tracker/node_modules/uuid/dist/esm/validate.js"], "sourcesContent": ["import REGEX from './regex.js';\nfunction validate(uuid) {\n    return typeof uuid === 'string' && REGEX.test(uuid);\n}\nexport default validate;\n"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,SAAS,IAAI;IAClB,OAAO,OAAO,SAAS,YAAY,4IAAA,CAAA,UAAK,CAAC,IAAI,CAAC;AAClD;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1441, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Doug/New%20folder/time-tracker/node_modules/uuid/dist/esm/stringify.js"], "sourcesContent": ["import validate from './validate.js';\nconst byteToHex = [];\nfor (let i = 0; i < 256; ++i) {\n    byteToHex.push((i + 0x100).toString(16).slice(1));\n}\nexport function unsafeStringify(arr, offset = 0) {\n    return (byteToHex[arr[offset + 0]] +\n        byteToHex[arr[offset + 1]] +\n        byteToHex[arr[offset + 2]] +\n        byteToHex[arr[offset + 3]] +\n        '-' +\n        byteToHex[arr[offset + 4]] +\n        byteToHex[arr[offset + 5]] +\n        '-' +\n        byteToHex[arr[offset + 6]] +\n        byteToHex[arr[offset + 7]] +\n        '-' +\n        byteToHex[arr[offset + 8]] +\n        byteToHex[arr[offset + 9]] +\n        '-' +\n        byteToHex[arr[offset + 10]] +\n        byteToHex[arr[offset + 11]] +\n        byteToHex[arr[offset + 12]] +\n        byteToHex[arr[offset + 13]] +\n        byteToHex[arr[offset + 14]] +\n        byteToHex[arr[offset + 15]]).toLowerCase();\n}\nfunction stringify(arr, offset = 0) {\n    const uuid = unsafeStringify(arr, offset);\n    if (!validate(uuid)) {\n        throw TypeError('Stringified UUID is invalid');\n    }\n    return uuid;\n}\nexport default stringify;\n"], "names": [], "mappings": ";;;;AAAA;;AACA,MAAM,YAAY,EAAE;AACpB,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,EAAE,EAAG;IAC1B,UAAU,IAAI,CAAC,CAAC,IAAI,KAAK,EAAE,QAAQ,CAAC,IAAI,KAAK,CAAC;AAClD;AACO,SAAS,gBAAgB,GAAG,EAAE,SAAS,CAAC;IAC3C,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAC9B,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAC1B,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAC1B,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAC1B,MACA,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAC1B,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAC1B,MACA,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAC1B,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAC1B,MACA,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAC1B,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAC1B,MACA,SAAS,CAAC,GAAG,CAAC,SAAS,GAAG,CAAC,GAC3B,SAAS,CAAC,GAAG,CAAC,SAAS,GAAG,CAAC,GAC3B,SAAS,CAAC,GAAG,CAAC,SAAS,GAAG,CAAC,GAC3B,SAAS,CAAC,GAAG,CAAC,SAAS,GAAG,CAAC,GAC3B,SAAS,CAAC,GAAG,CAAC,SAAS,GAAG,CAAC,GAC3B,SAAS,CAAC,GAAG,CAAC,SAAS,GAAG,CAAC,EAAE,WAAW;AAChD;AACA,SAAS,UAAU,GAAG,EAAE,SAAS,CAAC;IAC9B,MAAM,OAAO,gBAAgB,KAAK;IAClC,IAAI,CAAC,CAAA,GAAA,+IAAA,CAAA,UAAQ,AAAD,EAAE,OAAO;QACjB,MAAM,UAAU;IACpB;IACA,OAAO;AACX;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1468, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Doug/New%20folder/time-tracker/node_modules/uuid/dist/esm/v4.js"], "sourcesContent": ["import native from './native.js';\nimport rng from './rng.js';\nimport { unsafeStringify } from './stringify.js';\nfunction v4(options, buf, offset) {\n    if (native.randomUUID && !buf && !options) {\n        return native.randomUUID();\n    }\n    options = options || {};\n    const rnds = options.random ?? options.rng?.() ?? rng();\n    if (rnds.length < 16) {\n        throw new Error('Random bytes length must be >= 16');\n    }\n    rnds[6] = (rnds[6] & 0x0f) | 0x40;\n    rnds[8] = (rnds[8] & 0x3f) | 0x80;\n    if (buf) {\n        offset = offset || 0;\n        if (offset < 0 || offset + 16 > buf.length) {\n            throw new RangeError(`UUID byte range ${offset}:${offset + 15} is out of buffer bounds`);\n        }\n        for (let i = 0; i < 16; ++i) {\n            buf[offset + i] = rnds[i];\n        }\n        return buf;\n    }\n    return unsafeStringify(rnds);\n}\nexport default v4;\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AACA,SAAS,GAAG,OAAO,EAAE,GAAG,EAAE,MAAM;IAC5B,IAAI,6IAAA,CAAA,UAAM,CAAC,UAAU,IAAI,CAAC,OAAO,CAAC,SAAS;QACvC,OAAO,6IAAA,CAAA,UAAM,CAAC,UAAU;IAC5B;IACA,UAAU,WAAW,CAAC;IACtB,MAAM,OAAO,QAAQ,MAAM,IAAI,QAAQ,GAAG,QAAQ,CAAA,GAAA,0IAAA,CAAA,UAAG,AAAD;IACpD,IAAI,KAAK,MAAM,GAAG,IAAI;QAClB,MAAM,IAAI,MAAM;IACpB;IACA,IAAI,CAAC,EAAE,GAAG,AAAC,IAAI,CAAC,EAAE,GAAG,OAAQ;IAC7B,IAAI,CAAC,EAAE,GAAG,AAAC,IAAI,CAAC,EAAE,GAAG,OAAQ;IAC7B,IAAI,KAAK;QACL,SAAS,UAAU;QACnB,IAAI,SAAS,KAAK,SAAS,KAAK,IAAI,MAAM,EAAE;YACxC,MAAM,IAAI,WAAW,CAAC,gBAAgB,EAAE,OAAO,CAAC,EAAE,SAAS,GAAG,wBAAwB,CAAC;QAC3F;QACA,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,EAAE,EAAG;YACzB,GAAG,CAAC,SAAS,EAAE,GAAG,IAAI,CAAC,EAAE;QAC7B;QACA,OAAO;IACX;IACA,OAAO,CAAA,GAAA,gJAAA,CAAA,kBAAe,AAAD,EAAE;AAC3B;uCACe", "ignoreList": [0], "debugId": null}}]}