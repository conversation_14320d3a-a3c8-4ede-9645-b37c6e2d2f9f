{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Doug/New%20folder/time-tracker/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\";\nimport { twMerge } from \"tailwind-merge\";\nimport { format, formatDistanceToNow, differenceInMinutes } from 'date-fns';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n\nexport const formatTime = (minutes: number): string => {\n  const hours = Math.floor(minutes / 60);\n  const mins = minutes % 60;\n  \n  if (hours === 0) {\n    return `${mins}m`;\n  }\n  \n  return `${hours}h ${mins}m`;\n};\n\nexport const formatDate = (dateString: string): string => {\n  return format(new Date(dateString), 'MMM dd, yyyy');\n};\n\nexport const formatDateTime = (dateString: string): string => {\n  return format(new Date(dateString), 'MMM dd, yyyy HH:mm');\n};\n\nexport const formatRelativeTime = (dateString: string): string => {\n  return formatDistanceToNow(new Date(dateString), { addSuffix: true });\n};\n\nexport const calculateTimeDifference = (startTime: string, endTime?: string): number => {\n  const start = new Date(startTime);\n  const end = endTime ? new Date(endTime) : new Date();\n  return differenceInMinutes(end, start);\n};\n\nexport const getStatusColor = (status: string): string => {\n  switch (status) {\n    case 'pending':\n      return 'bg-gray-100 text-gray-800';\n    case 'in-progress':\n      return 'bg-blue-100 text-blue-800';\n    case 'waiting-parts':\n      return 'bg-yellow-100 text-yellow-800';\n    case 'completed':\n      return 'bg-green-100 text-green-800';\n    case 'delivered':\n      return 'bg-purple-100 text-purple-800';\n    default:\n      return 'bg-gray-100 text-gray-800';\n  }\n};\n\nexport const getPriorityColor = (priority: string): string => {\n  switch (priority) {\n    case 'low':\n      return 'bg-green-100 text-green-800';\n    case 'medium':\n      return 'bg-yellow-100 text-yellow-800';\n    case 'high':\n      return 'bg-orange-100 text-orange-800';\n    case 'urgent':\n      return 'bg-red-100 text-red-800';\n    default:\n      return 'bg-gray-100 text-gray-800';\n  }\n};\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;AACA;AACA;AAAA;AAAA;;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,MAAM,aAAa,CAAC;IACzB,MAAM,QAAQ,KAAK,KAAK,CAAC,UAAU;IACnC,MAAM,OAAO,UAAU;IAEvB,IAAI,UAAU,GAAG;QACf,OAAO,GAAG,KAAK,CAAC,CAAC;IACnB;IAEA,OAAO,GAAG,MAAM,EAAE,EAAE,KAAK,CAAC,CAAC;AAC7B;AAEO,MAAM,aAAa,CAAC;IACzB,OAAO,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,aAAa;AACtC;AAEO,MAAM,iBAAiB,CAAC;IAC7B,OAAO,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,aAAa;AACtC;AAEO,MAAM,qBAAqB,CAAC;IACjC,OAAO,CAAA,GAAA,qJAAA,CAAA,sBAAmB,AAAD,EAAE,IAAI,KAAK,aAAa;QAAE,WAAW;IAAK;AACrE;AAEO,MAAM,0BAA0B,CAAC,WAAmB;IACzD,MAAM,QAAQ,IAAI,KAAK;IACvB,MAAM,MAAM,UAAU,IAAI,KAAK,WAAW,IAAI;IAC9C,OAAO,CAAA,GAAA,qJAAA,CAAA,sBAAmB,AAAD,EAAE,KAAK;AAClC;AAEO,MAAM,iBAAiB,CAAC;IAC7B,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAEO,MAAM,mBAAmB,CAAC;IAC/B,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF", "debugId": null}}, {"offset": {"line": 91, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Doug/New%20folder/time-tracker/src/components/Navigation.tsx"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport { usePathname } from 'next/navigation';\nimport { Home, Clock, Plus, Settings } from 'lucide-react';\nimport { cn } from '@/lib/utils';\n\nconst Navigation = () => {\n  const pathname = usePathname();\n\n  const navItems = [\n    {\n      href: '/',\n      icon: Home,\n      label: 'Dashboard',\n    },\n    {\n      href: '/orders',\n      icon: Clock,\n      label: 'Orders',\n    },\n    {\n      href: '/orders/new',\n      icon: Plus,\n      label: 'New Order',\n    },\n    {\n      href: '/settings',\n      icon: Settings,\n      label: 'Settings',\n    },\n  ];\n\n  return (\n    <nav className=\"fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 px-4 py-2 z-50\">\n      <div className=\"flex justify-around items-center max-w-md mx-auto\">\n        {navItems.map((item) => {\n          const Icon = item.icon;\n          const isActive = pathname === item.href;\n          \n          return (\n            <Link\n              key={item.href}\n              href={item.href}\n              className={cn(\n                'flex flex-col items-center justify-center p-2 rounded-lg transition-colors',\n                isActive\n                  ? 'text-blue-600 bg-blue-50'\n                  : 'text-gray-600 hover:text-blue-600 hover:bg-gray-50'\n              )}\n            >\n              <Icon size={20} />\n              <span className=\"text-xs mt-1 font-medium\">{item.label}</span>\n            </Link>\n          );\n        })}\n      </div>\n    </nav>\n  );\n};\n\nexport default Navigation;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AACA;;;AALA;;;;;AAOA,MAAM,aAAa;;IACjB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,WAAW;QACf;YACE,MAAM;YACN,MAAM,sMAAA,CAAA,OAAI;YACV,OAAO;QACT;QACA;YACE,MAAM;YACN,MAAM,uMAAA,CAAA,QAAK;YACX,OAAO;QACT;QACA;YACE,MAAM;YACN,MAAM,qMAAA,CAAA,OAAI;YACV,OAAO;QACT;QACA;YACE,MAAM;YACN,MAAM,6MAAA,CAAA,WAAQ;YACd,OAAO;QACT;KACD;IAED,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;sBACZ,SAAS,GAAG,CAAC,CAAC;gBACb,MAAM,OAAO,KAAK,IAAI;gBACtB,MAAM,WAAW,aAAa,KAAK,IAAI;gBAEvC,qBACE,6LAAC,+JAAA,CAAA,UAAI;oBAEH,MAAM,KAAK,IAAI;oBACf,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8EACA,WACI,6BACA;;sCAGN,6LAAC;4BAAK,MAAM;;;;;;sCACZ,6LAAC;4BAAK,WAAU;sCAA4B,KAAK,KAAK;;;;;;;mBAVjD,KAAK,IAAI;;;;;YAapB;;;;;;;;;;;AAIR;GApDM;;QACa,qIAAA,CAAA,cAAW;;;KADxB;uCAsDS", "debugId": null}}]}