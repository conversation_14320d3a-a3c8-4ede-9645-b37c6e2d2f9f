{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Doug/New%20folder/time-tracker/src/lib/storage.ts"], "sourcesContent": ["import { Order, AppState } from '@/types';\n\nconst STORAGE_KEY = 'time-tracker-data';\n\nexport const loadAppState = (): AppState => {\n  if (typeof window === 'undefined') {\n    return { orders: [] };\n  }\n  \n  try {\n    const stored = localStorage.getItem(STORAGE_KEY);\n    if (stored) {\n      return JSON.parse(stored);\n    }\n  } catch (error) {\n    console.error('Error loading app state:', error);\n  }\n  \n  return { orders: [] };\n};\n\nexport const saveAppState = (state: AppState): void => {\n  if (typeof window === 'undefined') return;\n  \n  try {\n    localStorage.setItem(STORAGE_KEY, JSON.stringify(state));\n  } catch (error) {\n    console.error('Error saving app state:', error);\n  }\n};\n\nexport const saveOrder = (order: Order): void => {\n  const state = loadAppState();\n  const existingIndex = state.orders.findIndex(o => o.id === order.id);\n  \n  if (existingIndex >= 0) {\n    state.orders[existingIndex] = order;\n  } else {\n    state.orders.push(order);\n  }\n  \n  saveAppState(state);\n};\n\nexport const deleteOrder = (orderId: string): void => {\n  const state = loadAppState();\n  state.orders = state.orders.filter(o => o.id !== orderId);\n  saveAppState(state);\n};\n\nexport const getOrder = (orderId: string): Order | undefined => {\n  const state = loadAppState();\n  return state.orders.find(o => o.id === orderId);\n};\n"], "names": [], "mappings": ";;;;;;;AAEA,MAAM,cAAc;AAEb,MAAM,eAAe;IAC1B,wCAAmC;QACjC,OAAO;YAAE,QAAQ,EAAE;QAAC;IACtB;;AAYF;AAEO,MAAM,eAAe,CAAC;IAC3B,wCAAmC;;AAOrC;AAEO,MAAM,YAAY,CAAC;IACxB,MAAM,QAAQ;IACd,MAAM,gBAAgB,MAAM,MAAM,CAAC,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,MAAM,EAAE;IAEnE,IAAI,iBAAiB,GAAG;QACtB,MAAM,MAAM,CAAC,cAAc,GAAG;IAChC,OAAO;QACL,MAAM,MAAM,CAAC,IAAI,CAAC;IACpB;IAEA,aAAa;AACf;AAEO,MAAM,cAAc,CAAC;IAC1B,MAAM,QAAQ;IACd,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;IACjD,aAAa;AACf;AAEO,MAAM,WAAW,CAAC;IACvB,MAAM,QAAQ;IACd,OAAO,MAAM,MAAM,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;AACzC", "debugId": null}}, {"offset": {"line": 52, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Doug/New%20folder/time-tracker/src/app/settings/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { Download, Upload, Trash2, Info } from 'lucide-react';\nimport { loadAppState, saveAppState } from '@/lib/storage';\n\nexport default function SettingsPage() {\n  const [showExportData, setShowExportData] = useState(false);\n\n  const exportData = () => {\n    const state = loadAppState();\n    const dataStr = JSON.stringify(state, null, 2);\n    const dataBlob = new Blob([dataStr], { type: 'application/json' });\n    const url = URL.createObjectURL(dataBlob);\n    \n    const link = document.createElement('a');\n    link.href = url;\n    link.download = `time-tracker-backup-${new Date().toISOString().split('T')[0]}.json`;\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n    URL.revokeObjectURL(url);\n  };\n\n  const importData = (event: React.ChangeEvent<HTMLInputElement>) => {\n    const file = event.target.files?.[0];\n    if (!file) return;\n\n    const reader = new FileReader();\n    reader.onload = (e) => {\n      try {\n        const data = JSON.parse(e.target?.result as string);\n        if (data.orders && Array.isArray(data.orders)) {\n          saveAppState(data);\n          alert('Data imported successfully! Please refresh the page.');\n        } else {\n          alert('Invalid file format. Please select a valid backup file.');\n        }\n      } catch (error) {\n        alert('Error reading file. Please make sure it\\'s a valid JSON backup.');\n      }\n    };\n    reader.readAsText(file);\n  };\n\n  const clearAllData = () => {\n    if (window.confirm('Are you sure you want to delete all data? This action cannot be undone.')) {\n      localStorage.clear();\n      alert('All data has been cleared. Please refresh the page.');\n    }\n  };\n\n  return (\n    <div className=\"max-w-md mx-auto p-4 space-y-6\">\n      {/* Header */}\n      <div className=\"text-center space-y-2\">\n        <h1 className=\"text-2xl font-bold text-gray-900\">Settings</h1>\n        <p className=\"text-gray-600\">Manage your app preferences and data</p>\n      </div>\n\n      {/* Data Management */}\n      <div className=\"bg-white rounded-lg shadow-sm border p-4 space-y-4\">\n        <h2 className=\"text-lg font-semibold text-gray-900\">Data Management</h2>\n        \n        <div className=\"space-y-3\">\n          <button\n            onClick={exportData}\n            className=\"w-full flex items-center gap-3 p-3 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors\"\n          >\n            <Download size={20} className=\"text-blue-600\" />\n            <div className=\"text-left\">\n              <p className=\"font-medium text-gray-900\">Export Data</p>\n              <p className=\"text-sm text-gray-600\">Download a backup of all your orders</p>\n            </div>\n          </button>\n\n          <div className=\"relative\">\n            <input\n              type=\"file\"\n              accept=\".json\"\n              onChange={importData}\n              className=\"absolute inset-0 w-full h-full opacity-0 cursor-pointer\"\n            />\n            <div className=\"w-full flex items-center gap-3 p-3 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer\">\n              <Upload size={20} className=\"text-green-600\" />\n              <div className=\"text-left\">\n                <p className=\"font-medium text-gray-900\">Import Data</p>\n                <p className=\"text-sm text-gray-600\">Restore from a backup file</p>\n              </div>\n            </div>\n          </div>\n\n          <button\n            onClick={clearAllData}\n            className=\"w-full flex items-center gap-3 p-3 border border-red-300 rounded-lg hover:bg-red-50 transition-colors text-red-700\"\n          >\n            <Trash2 size={20} />\n            <div className=\"text-left\">\n              <p className=\"font-medium\">Clear All Data</p>\n              <p className=\"text-sm text-red-600\">Permanently delete all orders and data</p>\n            </div>\n          </button>\n        </div>\n      </div>\n\n      {/* App Info */}\n      <div className=\"bg-white rounded-lg shadow-sm border p-4 space-y-4\">\n        <div className=\"flex items-center gap-2\">\n          <Info size={20} className=\"text-gray-600\" />\n          <h2 className=\"text-lg font-semibold text-gray-900\">About</h2>\n        </div>\n        \n        <div className=\"space-y-3 text-sm text-gray-600\">\n          <div>\n            <p className=\"font-medium text-gray-900\">Time Tracker</p>\n            <p>Version 1.0.0</p>\n          </div>\n          \n          <div>\n            <p className=\"font-medium text-gray-900\">Features</p>\n            <ul className=\"list-disc list-inside space-y-1 mt-1\">\n              <li>Track time on business orders</li>\n              <li>Manage client information</li>\n              <li>Monitor parts and materials</li>\n              <li>Mobile-first responsive design</li>\n              <li>Local data storage</li>\n            </ul>\n          </div>\n          \n          <div>\n            <p className=\"font-medium text-gray-900\">Data Storage</p>\n            <p>All data is stored locally in your browser. No data is sent to external servers.</p>\n          </div>\n        </div>\n      </div>\n\n      {/* Tips */}\n      <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-4\">\n        <h3 className=\"font-medium text-blue-900 mb-2\">💡 Tips</h3>\n        <ul className=\"text-sm text-blue-800 space-y-1\">\n          <li>• Export your data regularly as a backup</li>\n          <li>• Use the priority system to organize urgent orders</li>\n          <li>• Track parts delivery dates to stay organized</li>\n          <li>• Add detailed descriptions for better tracking</li>\n        </ul>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AACA;AAJA;;;;;AAMe,SAAS;IACtB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,MAAM,aAAa;QACjB,MAAM,QAAQ,CAAA,GAAA,qHAAA,CAAA,eAAY,AAAD;QACzB,MAAM,UAAU,KAAK,SAAS,CAAC,OAAO,MAAM;QAC5C,MAAM,WAAW,IAAI,KAAK;YAAC;SAAQ,EAAE;YAAE,MAAM;QAAmB;QAChE,MAAM,MAAM,IAAI,eAAe,CAAC;QAEhC,MAAM,OAAO,SAAS,aAAa,CAAC;QACpC,KAAK,IAAI,GAAG;QACZ,KAAK,QAAQ,GAAG,CAAC,oBAAoB,EAAE,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC;QACpF,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,KAAK,KAAK;QACV,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,IAAI,eAAe,CAAC;IACtB;IAEA,MAAM,aAAa,CAAC;QAClB,MAAM,OAAO,MAAM,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;QACpC,IAAI,CAAC,MAAM;QAEX,MAAM,SAAS,IAAI;QACnB,OAAO,MAAM,GAAG,CAAC;YACf,IAAI;gBACF,MAAM,OAAO,KAAK,KAAK,CAAC,EAAE,MAAM,EAAE;gBAClC,IAAI,KAAK,MAAM,IAAI,MAAM,OAAO,CAAC,KAAK,MAAM,GAAG;oBAC7C,CAAA,GAAA,qHAAA,CAAA,eAAY,AAAD,EAAE;oBACb,MAAM;gBACR,OAAO;oBACL,MAAM;gBACR;YACF,EAAE,OAAO,OAAO;gBACd,MAAM;YACR;QACF;QACA,OAAO,UAAU,CAAC;IACpB;IAEA,MAAM,eAAe;QACnB,IAAI,OAAO,OAAO,CAAC,4EAA4E;YAC7F,aAAa,KAAK;YAClB,MAAM;QACR;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAmC;;;;;;kCACjD,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;0BAI/B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAsC;;;;;;kCAEpD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,SAAS;gCACT,WAAU;;kDAEV,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,MAAM;wCAAI,WAAU;;;;;;kDAC9B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAA4B;;;;;;0DACzC,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;0CAIzC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,MAAK;wCACL,QAAO;wCACP,UAAU;wCACV,WAAU;;;;;;kDAEZ,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,sMAAA,CAAA,SAAM;gDAAC,MAAM;gDAAI,WAAU;;;;;;0DAC5B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;kEAA4B;;;;;;kEACzC,8OAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;;;;;;;;;;;;;0CAK3C,8OAAC;gCACC,SAAS;gCACT,WAAU;;kDAEV,8OAAC,0MAAA,CAAA,SAAM;wCAAC,MAAM;;;;;;kDACd,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAc;;;;;;0DAC3B,8OAAC;gDAAE,WAAU;0DAAuB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO5C,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kMAAA,CAAA,OAAI;gCAAC,MAAM;gCAAI,WAAU;;;;;;0CAC1B,8OAAC;gCAAG,WAAU;0CAAsC;;;;;;;;;;;;kCAGtD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAE,WAAU;kDAA4B;;;;;;kDACzC,8OAAC;kDAAE;;;;;;;;;;;;0CAGL,8OAAC;;kDACC,8OAAC;wCAAE,WAAU;kDAA4B;;;;;;kDACzC,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;;;;;;;;;;;;;0CAIR,8OAAC;;kDACC,8OAAC;wCAAE,WAAU;kDAA4B;;;;;;kDACzC,8OAAC;kDAAE;;;;;;;;;;;;;;;;;;;;;;;;0BAMT,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAiC;;;;;;kCAC/C,8OAAC;wBAAG,WAAU;;0CACZ,8OAAC;0CAAG;;;;;;0CACJ,8OAAC;0CAAG;;;;;;0CACJ,8OAAC;0CAAG;;;;;;0CACJ,8OAAC;0CAAG;;;;;;;;;;;;;;;;;;;;;;;;AAKd", "debugId": null}}, {"offset": {"line": 527, "column": 0}, "map": {"version": 3, "file": "download.js", "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Doug/New%20folder/time-tracker/node_modules/lucide-react/src/icons/download.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M12 15V3', key: 'm9g1x1' }],\n  ['path', { d: 'M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4', key: 'ih7n3h' }],\n  ['path', { d: 'm7 10 5 5 5-5', key: 'brsn70' }],\n];\n\n/**\n * @component @name Download\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIgMTVWMyIgLz4KICA8cGF0aCBkPSJNMjEgMTV2NGEyIDIgMCAwIDEtMiAySDVhMiAyIDAgMCAxLTItMnYtNCIgLz4KICA8cGF0aCBkPSJtNyAxMCA1IDUgNS01IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/download\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Download = createLucideIcon('download', __iconNode);\n\nexport default Download;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACzC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA6C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC1E;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CAChD;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,QAAA,CAAW,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAA,AAAjB,CAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAY,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 580, "column": 0}, "map": {"version": 3, "file": "upload.js", "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Doug/New%20folder/time-tracker/node_modules/lucide-react/src/icons/upload.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M12 3v12', key: '1x0j5s' }],\n  ['path', { d: 'm17 8-5-5-5 5', key: '7q97r8' }],\n  ['path', { d: 'M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4', key: 'ih7n3h' }],\n];\n\n/**\n * @component @name Upload\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIgM3YxMiIgLz4KICA8cGF0aCBkPSJtMTcgOC01LTUtNSA1IiAvPgogIDxwYXRoIGQ9Ik0yMSAxNXY0YTIgMiAwIDAgMS0yIDJINWEyIDIgMCAwIDEtMi0ydi00IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/upload\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Upload = createLucideIcon('upload', __iconNode);\n\nexport default Upload;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACzC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC9C;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA6C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CAC5E;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAS,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAU,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 633, "column": 0}, "map": {"version": 3, "file": "trash-2.js", "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Doug/New%20folder/time-tracker/node_modules/lucide-react/src/icons/trash-2.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M3 6h18', key: 'd0wm0j' }],\n  ['path', { d: 'M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6', key: '4alrt4' }],\n  ['path', { d: 'M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2', key: 'v07s0e' }],\n  ['line', { x1: '10', x2: '10', y1: '11', y2: '17', key: '1uufr5' }],\n  ['line', { x1: '14', x2: '14', y1: '11', y2: '17', key: 'xtxkd' }],\n];\n\n/**\n * @component @name Trash2\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMyA2aDE4IiAvPgogIDxwYXRoIGQ9Ik0xOSA2djE0YzAgMS0xIDItMiAySDdjLTEgMC0yLTEtMi0yVjYiIC8+CiAgPHBhdGggZD0iTTggNlY0YzAtMSAxLTIgMi0yaDRjMSAwIDIgMSAyIDJ2MiIgLz4KICA8bGluZSB4MT0iMTAiIHgyPSIxMCIgeTE9IjExIiB5Mj0iMTciIC8+CiAgPGxpbmUgeDE9IjE0IiB4Mj0iMTQiIHkxPSIxMSIgeTI9IjE3IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/trash-2\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Trash2 = createLucideIcon('trash-2', __iconNode);\n\nexport default Trash2;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACxC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAyC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACtE;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAsC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACnE;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAE;YAAA,CAAA,CAAA,EAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,EAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAClE;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAE;YAAA,CAAA,CAAA,EAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,EAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAS;KAAA;CACnE;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAS,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAW,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 706, "column": 0}, "map": {"version": 3, "file": "info.js", "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Doug/New%20folder/time-tracker/node_modules/lucide-react/src/icons/info.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['circle', { cx: '12', cy: '12', r: '10', key: '1mglay' }],\n  ['path', { d: 'M12 16v-4', key: '1dtifu' }],\n  ['path', { d: 'M12 8h.01', key: 'e9boi3' }],\n];\n\n/**\n * @component @name Info\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCIgLz4KICA8cGF0aCBkPSJNMTIgMTZ2LTQiIC8+CiAgPHBhdGggZD0iTTEyIDhoLjAxIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/info\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Info = createLucideIcon('info', __iconNode);\n\nexport default Info;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,QAAU,CAAA;QAAA,CAAA;YAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IACzD;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC1C;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CAC5C;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAO,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiB,AAAjB,CAAA,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}]}