{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Doug/New%20folder/time-tracker/src/lib/storage.ts"], "sourcesContent": ["import { Order, AppState } from '@/types';\n\nconst STORAGE_KEY = 'time-tracker-data';\n\nexport const loadAppState = (): AppState => {\n  if (typeof window === 'undefined') {\n    return { orders: [] };\n  }\n  \n  try {\n    const stored = localStorage.getItem(STORAGE_KEY);\n    if (stored) {\n      return JSON.parse(stored);\n    }\n  } catch (error) {\n    console.error('Error loading app state:', error);\n  }\n  \n  return { orders: [] };\n};\n\nexport const saveAppState = (state: AppState): void => {\n  if (typeof window === 'undefined') return;\n  \n  try {\n    localStorage.setItem(STORAGE_KEY, JSON.stringify(state));\n  } catch (error) {\n    console.error('Error saving app state:', error);\n  }\n};\n\nexport const saveOrder = (order: Order): void => {\n  const state = loadAppState();\n  const existingIndex = state.orders.findIndex(o => o.id === order.id);\n  \n  if (existingIndex >= 0) {\n    state.orders[existingIndex] = order;\n  } else {\n    state.orders.push(order);\n  }\n  \n  saveAppState(state);\n};\n\nexport const deleteOrder = (orderId: string): void => {\n  const state = loadAppState();\n  state.orders = state.orders.filter(o => o.id !== orderId);\n  saveAppState(state);\n};\n\nexport const getOrder = (orderId: string): Order | undefined => {\n  const state = loadAppState();\n  return state.orders.find(o => o.id === orderId);\n};\n"], "names": [], "mappings": ";;;;;;;AAEA,MAAM,cAAc;AAEb,MAAM,eAAe;IAC1B,uCAAmC;;IAEnC;IAEA,IAAI;QACF,MAAM,SAAS,aAAa,OAAO,CAAC;QACpC,IAAI,QAAQ;YACV,OAAO,KAAK,KAAK,CAAC;QACpB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;IAC5C;IAEA,OAAO;QAAE,QAAQ,EAAE;IAAC;AACtB;AAEO,MAAM,eAAe,CAAC;IAC3B,uCAAmC;;IAAM;IAEzC,IAAI;QACF,aAAa,OAAO,CAAC,aAAa,KAAK,SAAS,CAAC;IACnD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;IAC3C;AACF;AAEO,MAAM,YAAY,CAAC;IACxB,MAAM,QAAQ;IACd,MAAM,gBAAgB,MAAM,MAAM,CAAC,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,MAAM,EAAE;IAEnE,IAAI,iBAAiB,GAAG;QACtB,MAAM,MAAM,CAAC,cAAc,GAAG;IAChC,OAAO;QACL,MAAM,MAAM,CAAC,IAAI,CAAC;IACpB;IAEA,aAAa;AACf;AAEO,MAAM,cAAc,CAAC;IAC1B,MAAM,QAAQ;IACd,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;IACjD,aAAa;AACf;AAEO,MAAM,WAAW,CAAC;IACvB,MAAM,QAAQ;IACd,OAAO,MAAM,MAAM,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;AACzC", "debugId": null}}, {"offset": {"line": 69, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Doug/New%20folder/time-tracker/src/components/OrderCard.tsx"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport { Clock, User, Package, AlertCircle } from 'lucide-react';\nimport { Order } from '@/types';\nimport { formatTime, formatRelativeTime, getStatusColor, getPriorityColor, cn } from '@/lib/utils';\n\ninterface OrderCardProps {\n  order: Order;\n}\n\nconst OrderCard = ({ order }: OrderCardProps) => {\n  const isActive = order.timeEntries.some(entry => entry.isActive);\n  const partsNeeded = order.parts.filter(part => part.quantity < part.quantityNeeded);\n  const hasPartsIssues = partsNeeded.length > 0;\n\n  return (\n    <Link href={`/orders/${order.id}`}>\n      <div className={cn(\n        \"bg-white rounded-lg shadow-sm border p-4 space-y-3 hover:shadow-md transition-shadow\",\n        isActive && \"ring-2 ring-blue-500 ring-opacity-50\"\n      )}>\n        {/* Header */}\n        <div className=\"flex items-start justify-between\">\n          <div className=\"flex-1 min-w-0\">\n            <h3 className=\"text-lg font-semibold text-gray-900 truncate\">\n              {order.title}\n            </h3>\n            <p className=\"text-sm text-gray-600 mt-1 line-clamp-2\">\n              {order.description || 'No description'}\n            </p>\n          </div>\n          {isActive && (\n            <div className=\"flex items-center gap-1 bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-medium ml-2\">\n              <div className=\"w-2 h-2 bg-green-500 rounded-full animate-pulse\"></div>\n              Active\n            </div>\n          )}\n        </div>\n\n        {/* Client Info */}\n        <div className=\"flex items-center gap-2 text-sm text-gray-600\">\n          <User size={16} />\n          <span>{order.client.name}</span>\n          {order.client.company && (\n            <span className=\"text-gray-400\">• {order.client.company}</span>\n          )}\n        </div>\n\n        {/* Status and Priority */}\n        <div className=\"flex items-center gap-2\">\n          <span className={cn(\n            \"px-2 py-1 rounded-full text-xs font-medium\",\n            getStatusColor(order.status)\n          )}>\n            {order.status.replace('-', ' ').toUpperCase()}\n          </span>\n          <span className={cn(\n            \"px-2 py-1 rounded-full text-xs font-medium\",\n            getPriorityColor(order.priority)\n          )}>\n            {order.priority.toUpperCase()}\n          </span>\n        </div>\n\n        {/* Parts Status */}\n        {hasPartsIssues && (\n          <div className=\"flex items-center gap-2 text-sm text-amber-600 bg-amber-50 p-2 rounded-lg\">\n            <AlertCircle size={16} />\n            <span>{partsNeeded.length} part{partsNeeded.length !== 1 ? 's' : ''} needed</span>\n          </div>\n        )}\n\n        {/* Time and Parts Summary */}\n        <div className=\"flex items-center justify-between text-sm text-gray-600 pt-2 border-t border-gray-100\">\n          <div className=\"flex items-center gap-1\">\n            <Clock size={16} />\n            <span>{formatTime(order.totalTimeSpent)}</span>\n          </div>\n          <div className=\"flex items-center gap-1\">\n            <Package size={16} />\n            <span>{order.parts.length} part{order.parts.length !== 1 ? 's' : ''}</span>\n          </div>\n          <div className=\"text-xs text-gray-500\">\n            {formatRelativeTime(order.updatedAt)}\n          </div>\n        </div>\n      </div>\n    </Link>\n  );\n};\n\nexport default OrderCard;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAEA;AALA;;;;;AAWA,MAAM,YAAY,CAAC,EAAE,KAAK,EAAkB;IAC1C,MAAM,WAAW,MAAM,WAAW,CAAC,IAAI,CAAC,CAAA,QAAS,MAAM,QAAQ;IAC/D,MAAM,cAAc,MAAM,KAAK,CAAC,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,GAAG,KAAK,cAAc;IAClF,MAAM,iBAAiB,YAAY,MAAM,GAAG;IAE5C,qBACE,6LAAC,+JAAA,CAAA,UAAI;QAAC,MAAM,CAAC,QAAQ,EAAE,MAAM,EAAE,EAAE;kBAC/B,cAAA,6LAAC;YAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,wFACA,YAAY;;8BAGZ,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CACX,MAAM,KAAK;;;;;;8CAEd,6LAAC;oCAAE,WAAU;8CACV,MAAM,WAAW,IAAI;;;;;;;;;;;;wBAGzB,0BACC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;;;;;gCAAwD;;;;;;;;;;;;;8BAO7E,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,qMAAA,CAAA,OAAI;4BAAC,MAAM;;;;;;sCACZ,6LAAC;sCAAM,MAAM,MAAM,CAAC,IAAI;;;;;;wBACvB,MAAM,MAAM,CAAC,OAAO,kBACnB,6LAAC;4BAAK,WAAU;;gCAAgB;gCAAG,MAAM,MAAM,CAAC,OAAO;;;;;;;;;;;;;8BAK3D,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAChB,8CACA,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,MAAM;sCAE1B,MAAM,MAAM,CAAC,OAAO,CAAC,KAAK,KAAK,WAAW;;;;;;sCAE7C,6LAAC;4BAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAChB,8CACA,CAAA,GAAA,sHAAA,CAAA,mBAAgB,AAAD,EAAE,MAAM,QAAQ;sCAE9B,MAAM,QAAQ,CAAC,WAAW;;;;;;;;;;;;gBAK9B,gCACC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,uNAAA,CAAA,cAAW;4BAAC,MAAM;;;;;;sCACnB,6LAAC;;gCAAM,YAAY,MAAM;gCAAC;gCAAM,YAAY,MAAM,KAAK,IAAI,MAAM;gCAAG;;;;;;;;;;;;;8BAKxE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,uMAAA,CAAA,QAAK;oCAAC,MAAM;;;;;;8CACb,6LAAC;8CAAM,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE,MAAM,cAAc;;;;;;;;;;;;sCAExC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,2MAAA,CAAA,UAAO;oCAAC,MAAM;;;;;;8CACf,6LAAC;;wCAAM,MAAM,KAAK,CAAC,MAAM;wCAAC;wCAAM,MAAM,KAAK,CAAC,MAAM,KAAK,IAAI,MAAM;;;;;;;;;;;;;sCAEnE,6LAAC;4BAAI,WAAU;sCACZ,CAAA,GAAA,sHAAA,CAAA,qBAAkB,AAAD,EAAE,MAAM,SAAS;;;;;;;;;;;;;;;;;;;;;;;AAM/C;KA/EM;uCAiFS", "debugId": null}}, {"offset": {"line": 323, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Doug/New%20folder/time-tracker/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport Link from 'next/link';\nimport { Clock, Plus, TrendingUp, Package, AlertCircle } from 'lucide-react';\nimport { Order } from '@/types';\nimport { loadAppState } from '@/lib/storage';\nimport { formatTime } from '@/lib/utils';\nimport OrderCard from '@/components/OrderCard';\n\nexport default function Home() {\n  const [orders, setOrders] = useState<Order[]>([]);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    const state = loadAppState();\n    setOrders(state.orders);\n    setLoading(false);\n  }, []);\n\n  const activeOrders = orders.filter(order =>\n    order.status === 'in-progress' || order.timeEntries.some(entry => entry.isActive)\n  );\n\n  const recentOrders = orders\n    .sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime())\n    .slice(0, 3);\n\n  const totalTimeToday = orders.reduce((total, order) => {\n    const today = new Date().toDateString();\n    const todayEntries = order.timeEntries.filter(entry =>\n      new Date(entry.startTime).toDateString() === today && !entry.isActive\n    );\n    return total + todayEntries.reduce((entryTotal, entry) => {\n      if (entry.endTime) {\n        const start = new Date(entry.startTime);\n        const end = new Date(entry.endTime);\n        return entryTotal + Math.floor((end.getTime() - start.getTime()) / (1000 * 60));\n      }\n      return entryTotal;\n    }, 0);\n  }, 0);\n\n  const partsNeeded = orders.reduce((total, order) => {\n    return total + order.parts.filter(part => part.quantity < part.quantityNeeded).length;\n  }, 0);\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center min-h-screen\">\n        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"max-w-md mx-auto p-4 space-y-6\">\n      {/* Header */}\n      <div className=\"text-center space-y-2\">\n        <h1 className=\"text-2xl font-bold text-gray-900\">Time Tracker</h1>\n        <p className=\"text-gray-600\">Manage your business orders efficiently</p>\n      </div>\n\n      {/* Stats Cards */}\n      <div className=\"grid grid-cols-2 gap-4\">\n        <div className=\"bg-white rounded-lg shadow-sm border p-4 text-center\">\n          <div className=\"flex items-center justify-center w-8 h-8 bg-blue-100 rounded-full mx-auto mb-2\">\n            <Clock size={16} className=\"text-blue-600\" />\n          </div>\n          <p className=\"text-2xl font-bold text-gray-900\">{formatTime(totalTimeToday)}</p>\n          <p className=\"text-sm text-gray-600\">Today</p>\n        </div>\n\n        <div className=\"bg-white rounded-lg shadow-sm border p-4 text-center\">\n          <div className=\"flex items-center justify-center w-8 h-8 bg-green-100 rounded-full mx-auto mb-2\">\n            <TrendingUp size={16} className=\"text-green-600\" />\n          </div>\n          <p className=\"text-2xl font-bold text-gray-900\">{activeOrders.length}</p>\n          <p className=\"text-sm text-gray-600\">Active</p>\n        </div>\n      </div>\n\n      {/* Parts Alert */}\n      {partsNeeded > 0 && (\n        <div className=\"bg-amber-50 border border-amber-200 rounded-lg p-4\">\n          <div className=\"flex items-center gap-2\">\n            <AlertCircle size={20} className=\"text-amber-600\" />\n            <div>\n              <p className=\"font-medium text-amber-800\">Parts Needed</p>\n              <p className=\"text-sm text-amber-700\">\n                {partsNeeded} part{partsNeeded !== 1 ? 's' : ''} required across your orders\n              </p>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Quick Actions */}\n      <div className=\"space-y-3\">\n        <h2 className=\"text-lg font-semibold text-gray-900\">Quick Actions</h2>\n        <Link\n          href=\"/orders/new\"\n          className=\"flex items-center gap-3 bg-blue-600 text-white p-4 rounded-lg hover:bg-blue-700 transition-colors\"\n        >\n          <Plus size={20} />\n          <span className=\"font-medium\">Add New Order</span>\n        </Link>\n      </div>\n\n      {/* Recent Orders */}\n      {recentOrders.length > 0 && (\n        <div className=\"space-y-3\">\n          <div className=\"flex items-center justify-between\">\n            <h2 className=\"text-lg font-semibold text-gray-900\">Recent Orders</h2>\n            <Link href=\"/orders\" className=\"text-blue-600 text-sm font-medium\">\n              View All\n            </Link>\n          </div>\n          <div className=\"space-y-3\">\n            {recentOrders.map(order => (\n              <OrderCard key={order.id} order={order} />\n            ))}\n          </div>\n        </div>\n      )}\n\n      {/* Empty State */}\n      {orders.length === 0 && (\n        <div className=\"text-center py-12 space-y-4\">\n          <div className=\"w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto\">\n            <Package size={24} className=\"text-gray-400\" />\n          </div>\n          <div>\n            <h3 className=\"text-lg font-medium text-gray-900\">No orders yet</h3>\n            <p className=\"text-gray-600 mt-1\">Create your first order to get started</p>\n          </div>\n          <Link\n            href=\"/orders/new\"\n            className=\"inline-flex items-center gap-2 bg-blue-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors\"\n          >\n            <Plus size={20} />\n            Add Your First Order\n          </Link>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAEA;AACA;AACA;;;AARA;;;;;;;AAUe,SAAS;;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW,EAAE;IAChD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,MAAM,QAAQ,CAAA,GAAA,wHAAA,CAAA,eAAY,AAAD;YACzB,UAAU,MAAM,MAAM;YACtB,WAAW;QACb;yBAAG,EAAE;IAEL,MAAM,eAAe,OAAO,MAAM,CAAC,CAAA,QACjC,MAAM,MAAM,KAAK,iBAAiB,MAAM,WAAW,CAAC,IAAI,CAAC,CAAA,QAAS,MAAM,QAAQ;IAGlF,MAAM,eAAe,OAClB,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO,IAC9E,KAAK,CAAC,GAAG;IAEZ,MAAM,iBAAiB,OAAO,MAAM,CAAC,CAAC,OAAO;QAC3C,MAAM,QAAQ,IAAI,OAAO,YAAY;QACrC,MAAM,eAAe,MAAM,WAAW,CAAC,MAAM,CAAC,CAAA,QAC5C,IAAI,KAAK,MAAM,SAAS,EAAE,YAAY,OAAO,SAAS,CAAC,MAAM,QAAQ;QAEvE,OAAO,QAAQ,aAAa,MAAM,CAAC,CAAC,YAAY;YAC9C,IAAI,MAAM,OAAO,EAAE;gBACjB,MAAM,QAAQ,IAAI,KAAK,MAAM,SAAS;gBACtC,MAAM,MAAM,IAAI,KAAK,MAAM,OAAO;gBAClC,OAAO,aAAa,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,MAAM,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE;YAC/E;YACA,OAAO;QACT,GAAG;IACL,GAAG;IAEH,MAAM,cAAc,OAAO,MAAM,CAAC,CAAC,OAAO;QACxC,OAAO,QAAQ,MAAM,KAAK,CAAC,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,GAAG,KAAK,cAAc,EAAE,MAAM;IACvF,GAAG;IAEH,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAmC;;;;;;kCACjD,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;0BAI/B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;oCAAC,MAAM;oCAAI,WAAU;;;;;;;;;;;0CAE7B,6LAAC;gCAAE,WAAU;0CAAoC,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE;;;;;;0CAC5D,6LAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;kCAGvC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,qNAAA,CAAA,aAAU;oCAAC,MAAM;oCAAI,WAAU;;;;;;;;;;;0CAElC,6LAAC;gCAAE,WAAU;0CAAoC,aAAa,MAAM;;;;;;0CACpE,6LAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;;;;;;;YAKxC,cAAc,mBACb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,uNAAA,CAAA,cAAW;4BAAC,MAAM;4BAAI,WAAU;;;;;;sCACjC,6LAAC;;8CACC,6LAAC;oCAAE,WAAU;8CAA6B;;;;;;8CAC1C,6LAAC;oCAAE,WAAU;;wCACV;wCAAY;wCAAM,gBAAgB,IAAI,MAAM;wCAAG;;;;;;;;;;;;;;;;;;;;;;;;0BAQ1D,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAsC;;;;;;kCACpD,6LAAC,+JAAA,CAAA,UAAI;wBACH,MAAK;wBACL,WAAU;;0CAEV,6LAAC,qMAAA,CAAA,OAAI;gCAAC,MAAM;;;;;;0CACZ,6LAAC;gCAAK,WAAU;0CAAc;;;;;;;;;;;;;;;;;;YAKjC,aAAa,MAAM,GAAG,mBACrB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAsC;;;;;;0CACpD,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAU,WAAU;0CAAoC;;;;;;;;;;;;kCAIrE,6LAAC;wBAAI,WAAU;kCACZ,aAAa,GAAG,CAAC,CAAA,sBAChB,6LAAC,kIAAA,CAAA,UAAS;gCAAgB,OAAO;+BAAjB,MAAM,EAAE;;;;;;;;;;;;;;;;YAO/B,OAAO,MAAM,KAAK,mBACjB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,2MAAA,CAAA,UAAO;4BAAC,MAAM;4BAAI,WAAU;;;;;;;;;;;kCAE/B,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAoC;;;;;;0CAClD,6LAAC;gCAAE,WAAU;0CAAqB;;;;;;;;;;;;kCAEpC,6LAAC,+JAAA,CAAA,UAAI;wBACH,MAAK;wBACL,WAAU;;0CAEV,6LAAC,qMAAA,CAAA,OAAI;gCAAC,MAAM;;;;;;4BAAM;;;;;;;;;;;;;;;;;;;AAO9B;GAzIwB;KAAA", "debugId": null}}, {"offset": {"line": 737, "column": 0}, "map": {"version": 3, "file": "trending-up.js", "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Doug/New%20folder/time-tracker/node_modules/lucide-react/src/icons/trending-up.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M16 7h6v6', key: 'box55l' }],\n  ['path', { d: 'm22 7-8.5 8.5-5-5L2 17', key: '1t1m79' }],\n];\n\n/**\n * @component @name TrendingUp\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTYgN2g2djYiIC8+CiAgPHBhdGggZD0ibTIyIDctOC41IDguNS01LTVMMiAxNyIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/trending-up\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst TrendingUp = createLucideIcon('trending-up', __iconNode);\n\nexport default TrendingUp;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC1C;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA0B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CACzD;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,UAAA,CAAa,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAiB,AAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAe,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 783, "column": 0}, "map": {"version": 3, "file": "package.js", "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Doug/New%20folder/time-tracker/node_modules/lucide-react/src/icons/package.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z',\n      key: '1a0edw',\n    },\n  ],\n  ['path', { d: 'M12 22V12', key: 'd0xqtd' }],\n  ['polyline', { points: '3.29 7 12 12 20.71 7', key: 'ousv84' }],\n  ['path', { d: 'm7.5 4.27 9 5.15', key: '1c824w' }],\n];\n\n/**\n * @component @name Package\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTEgMjEuNzNhMiAyIDAgMCAwIDIgMGw3LTRBMiAyIDAgMCAwIDIxIDE2VjhhMiAyIDAgMCAwLTEtMS43M2wtNy00YTIgMiAwIDAgMC0yIDBsLTcgNEEyIDIgMCAwIDAgMyA4djhhMiAyIDAgMCAwIDEgMS43M3oiIC8+CiAgPHBhdGggZD0iTTEyIDIyVjEyIiAvPgogIDxwb2x5bGluZSBwb2ludHM9IjMuMjkgNyAxMiAxMiAyMC43MSA3IiAvPgogIDxwYXRoIGQ9Im03LjUgNC4yNyA5IDUuMTUiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/package\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Package = createLucideIcon('package', __iconNode);\n\nexport default Package;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,CAAA,CAAA,CAAK,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACP;KACF;IACA;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC1C;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,CAAA;QAAA,CAAA;YAAE,QAAQ,CAAwB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC9D;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAoB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CACnD;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAU,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAW,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 843, "column": 0}, "map": {"version": 3, "file": "circle-alert.js", "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Doug/New%20folder/time-tracker/node_modules/lucide-react/src/icons/circle-alert.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['circle', { cx: '12', cy: '12', r: '10', key: '1mglay' }],\n  ['line', { x1: '12', x2: '12', y1: '8', y2: '12', key: '1pkeuh' }],\n  ['line', { x1: '12', x2: '12.01', y1: '16', y2: '16', key: '4dfq90' }],\n];\n\n/**\n * @component @name CircleAlert\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCIgLz4KICA8bGluZSB4MT0iMTIiIHgyPSIxMiIgeTE9IjgiIHkyPSIxMiIgLz4KICA8bGluZSB4MT0iMTIiIHgyPSIxMi4wMSIgeTE9IjE2IiB5Mj0iMTYiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/circle-alert\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst CircleAlert = createLucideIcon('circle-alert', __iconNode);\n\nexport default CircleAlert;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,QAAU,CAAA;QAAA,CAAA;YAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IACzD;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAE;YAAA,CAAA,CAAA,EAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,EAAA,CAAI,CAAA,CAAA,CAAA,CAAK,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACjE;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAE;YAAA,CAAA,CAAA,EAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,EAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CACvE;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,WAAA,CAAc,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAgB,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 904, "column": 0}, "map": {"version": 3, "file": "user.js", "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Doug/New%20folder/time-tracker/node_modules/lucide-react/src/icons/user.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2', key: '975kel' }],\n  ['circle', { cx: '12', cy: '7', r: '4', key: '17ys0d' }],\n];\n\n/**\n * @component @name User\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTkgMjF2LTJhNCA0IDAgMCAwLTQtNEg5YTQgNCAwIDAgMC00IDR2MiIgLz4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjciIHI9IjQiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/user\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst User = createLucideIcon('user', __iconNode);\n\nexport default User;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA6C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC1E;QAAC,QAAU,CAAA;QAAA,CAAA;YAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAI,CAAA,CAAA,CAAA,GAAA,CAAK;YAAA,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA;YAAK,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;CACzD;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAO,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}]}