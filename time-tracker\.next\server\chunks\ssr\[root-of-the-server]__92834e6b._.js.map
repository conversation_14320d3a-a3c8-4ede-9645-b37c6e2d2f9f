{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Doug/New%20folder/time-tracker/src/lib/storage.ts"], "sourcesContent": ["import { Order, AppState } from '@/types';\n\nconst STORAGE_KEY = 'time-tracker-data';\n\nexport const loadAppState = (): AppState => {\n  if (typeof window === 'undefined') {\n    return { orders: [] };\n  }\n  \n  try {\n    const stored = localStorage.getItem(STORAGE_KEY);\n    if (stored) {\n      return JSON.parse(stored);\n    }\n  } catch (error) {\n    console.error('Error loading app state:', error);\n  }\n  \n  return { orders: [] };\n};\n\nexport const saveAppState = (state: AppState): void => {\n  if (typeof window === 'undefined') return;\n  \n  try {\n    localStorage.setItem(STORAGE_KEY, JSON.stringify(state));\n  } catch (error) {\n    console.error('Error saving app state:', error);\n  }\n};\n\nexport const saveOrder = (order: Order): void => {\n  const state = loadAppState();\n  const existingIndex = state.orders.findIndex(o => o.id === order.id);\n  \n  if (existingIndex >= 0) {\n    state.orders[existingIndex] = order;\n  } else {\n    state.orders.push(order);\n  }\n  \n  saveAppState(state);\n};\n\nexport const deleteOrder = (orderId: string): void => {\n  const state = loadAppState();\n  state.orders = state.orders.filter(o => o.id !== orderId);\n  saveAppState(state);\n};\n\nexport const getOrder = (orderId: string): Order | undefined => {\n  const state = loadAppState();\n  return state.orders.find(o => o.id === orderId);\n};\n"], "names": [], "mappings": ";;;;;;;AAEA,MAAM,cAAc;AAEb,MAAM,eAAe;IAC1B,wCAAmC;QACjC,OAAO;YAAE,QAAQ,EAAE;QAAC;IACtB;;AAYF;AAEO,MAAM,eAAe,CAAC;IAC3B,wCAAmC;;AAOrC;AAEO,MAAM,YAAY,CAAC;IACxB,MAAM,QAAQ;IACd,MAAM,gBAAgB,MAAM,MAAM,CAAC,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,MAAM,EAAE;IAEnE,IAAI,iBAAiB,GAAG;QACtB,MAAM,MAAM,CAAC,cAAc,GAAG;IAChC,OAAO;QACL,MAAM,MAAM,CAAC,IAAI,CAAC;IACpB;IAEA,aAAa;AACf;AAEO,MAAM,cAAc,CAAC;IAC1B,MAAM,QAAQ;IACd,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;IACjD,aAAa;AACf;AAEO,MAAM,WAAW,CAAC;IACvB,MAAM,QAAQ;IACd,OAAO,MAAM,MAAM,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;AACzC", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Doug/New%20folder/time-tracker/src/app/orders/new/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { ArrowLeft, Plus, Trash2 } from 'lucide-react';\nimport Link from 'next/link';\nimport { Order, Client, Part } from '@/types';\nimport { saveOrder } from '@/lib/storage';\nimport { v4 as uuidv4 } from 'uuid';\n\nexport default function NewOrderPage() {\n  const router = useRouter();\n  const [loading, setLoading] = useState(false);\n\n  // Form state\n  const [title, setTitle] = useState('');\n  const [description, setDescription] = useState('');\n  const [priority, setPriority] = useState<'low' | 'medium' | 'high' | 'urgent'>('medium');\n  const [estimatedDelivery, setEstimatedDelivery] = useState('');\n  const [notes, setNotes] = useState('');\n\n  // Client state\n  const [client, setClient] = useState<Partial<Client>>({\n    name: '',\n    email: '',\n    phone: '',\n    address: '',\n    company: '',\n  });\n\n  // Parts state\n  const [parts, setParts] = useState<Partial<Part>[]>([\n    {\n      name: '',\n      quantity: 0,\n      quantityNeeded: 1,\n      supplier: '',\n      expectedDelivery: '',\n      cost: 0,\n      notes: '',\n    }\n  ]);\n\n  const addPart = () => {\n    setParts([...parts, {\n      name: '',\n      quantity: 0,\n      quantityNeeded: 1,\n      supplier: '',\n      expectedDelivery: '',\n      cost: 0,\n      notes: '',\n    }]);\n  };\n\n  const removePart = (index: number) => {\n    setParts(parts.filter((_, i) => i !== index));\n  };\n\n  const updatePart = (index: number, field: keyof Part, value: any) => {\n    const updatedParts = [...parts];\n    updatedParts[index] = { ...updatedParts[index], [field]: value };\n    setParts(updatedParts);\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    if (!title.trim() || !client.name?.trim()) {\n      alert('Please fill in the order title and client name');\n      return;\n    }\n\n    setLoading(true);\n\n    try {\n      const newOrder: Order = {\n        id: uuidv4(),\n        title: title.trim(),\n        description: description.trim() || undefined,\n        client: {\n          id: uuidv4(),\n          name: client.name!.trim(),\n          email: client.email?.trim() || undefined,\n          phone: client.phone?.trim() || undefined,\n          address: client.address?.trim() || undefined,\n          company: client.company?.trim() || undefined,\n        },\n        status: 'pending',\n        priority,\n        createdAt: new Date().toISOString(),\n        updatedAt: new Date().toISOString(),\n        estimatedDelivery: estimatedDelivery || undefined,\n        parts: parts\n          .filter(part => part.name?.trim())\n          .map(part => ({\n            id: uuidv4(),\n            name: part.name!.trim(),\n            quantity: part.quantity || 0,\n            quantityNeeded: part.quantityNeeded || 1,\n            supplier: part.supplier?.trim() || undefined,\n            expectedDelivery: part.expectedDelivery || undefined,\n            cost: part.cost || undefined,\n            notes: part.notes?.trim() || undefined,\n          })),\n        timeEntries: [],\n        totalTimeSpent: 0,\n        notes: notes.trim() || undefined,\n      };\n\n      saveOrder(newOrder);\n      router.push(`/orders/${newOrder.id}`);\n    } catch (error) {\n      console.error('Error creating order:', error);\n      alert('Failed to create order. Please try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"max-w-md mx-auto p-4 space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center gap-3\">\n        <Link href=\"/orders\" className=\"p-2 hover:bg-gray-100 rounded-lg\">\n          <ArrowLeft size={20} />\n        </Link>\n        <h1 className=\"text-2xl font-bold text-gray-900\">New Order</h1>\n      </div>\n\n      <form onSubmit={handleSubmit} className=\"space-y-6\">\n        {/* Order Details */}\n        <div className=\"bg-white rounded-lg shadow-sm border p-4 space-y-4\">\n          <h2 className=\"text-lg font-semibold text-gray-900\">Order Details</h2>\n          \n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n              Order Title *\n            </label>\n            <input\n              type=\"text\"\n              value={title}\n              onChange={(e) => setTitle(e.target.value)}\n              className=\"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              placeholder=\"Enter order title\"\n              required\n            />\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n              Description\n            </label>\n            <textarea\n              value={description}\n              onChange={(e) => setDescription(e.target.value)}\n              className=\"w-full p-3 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              rows={3}\n              placeholder=\"Describe the order details\"\n            />\n          </div>\n\n          <div className=\"grid grid-cols-2 gap-3\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                Priority\n              </label>\n              <select\n                value={priority}\n                onChange={(e) => setPriority(e.target.value as any)}\n                className=\"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              >\n                <option value=\"low\">Low</option>\n                <option value=\"medium\">Medium</option>\n                <option value=\"high\">High</option>\n                <option value=\"urgent\">Urgent</option>\n              </select>\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                Est. Delivery\n              </label>\n              <input\n                type=\"date\"\n                value={estimatedDelivery}\n                onChange={(e) => setEstimatedDelivery(e.target.value)}\n                className=\"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              />\n            </div>\n          </div>\n        </div>\n\n        {/* Client Details */}\n        <div className=\"bg-white rounded-lg shadow-sm border p-4 space-y-4\">\n          <h2 className=\"text-lg font-semibold text-gray-900\">Client Details</h2>\n          \n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n              Client Name *\n            </label>\n            <input\n              type=\"text\"\n              value={client.name || ''}\n              onChange={(e) => setClient({ ...client, name: e.target.value })}\n              className=\"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              placeholder=\"Enter client name\"\n              required\n            />\n          </div>\n\n          <div className=\"grid grid-cols-2 gap-3\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                Email\n              </label>\n              <input\n                type=\"email\"\n                value={client.email || ''}\n                onChange={(e) => setClient({ ...client, email: e.target.value })}\n                className=\"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                placeholder=\"<EMAIL>\"\n              />\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                Phone\n              </label>\n              <input\n                type=\"tel\"\n                value={client.phone || ''}\n                onChange={(e) => setClient({ ...client, phone: e.target.value })}\n                className=\"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                placeholder=\"(*************\"\n              />\n            </div>\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n              Company\n            </label>\n            <input\n              type=\"text\"\n              value={client.company || ''}\n              onChange={(e) => setClient({ ...client, company: e.target.value })}\n              className=\"w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              placeholder=\"Company name\"\n            />\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n              Address\n            </label>\n            <textarea\n              value={client.address || ''}\n              onChange={(e) => setClient({ ...client, address: e.target.value })}\n              className=\"w-full p-3 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              rows={2}\n              placeholder=\"Client address\"\n            />\n          </div>\n        </div>\n\n        {/* Parts */}\n        <div className=\"bg-white rounded-lg shadow-sm border p-4 space-y-4\">\n          <div className=\"flex items-center justify-between\">\n            <h2 className=\"text-lg font-semibold text-gray-900\">Parts & Materials</h2>\n            <button\n              type=\"button\"\n              onClick={addPart}\n              className=\"flex items-center gap-1 text-blue-600 text-sm font-medium hover:text-blue-700\"\n            >\n              <Plus size={16} />\n              Add Part\n            </button>\n          </div>\n\n          {parts.map((part, index) => (\n            <div key={index} className=\"border border-gray-200 rounded-lg p-3 space-y-3\">\n              <div className=\"flex items-center justify-between\">\n                <span className=\"text-sm font-medium text-gray-700\">Part {index + 1}</span>\n                {parts.length > 1 && (\n                  <button\n                    type=\"button\"\n                    onClick={() => removePart(index)}\n                    className=\"text-red-600 hover:text-red-700\"\n                  >\n                    <Trash2 size={16} />\n                  </button>\n                )}\n              </div>\n\n              <div>\n                <input\n                  type=\"text\"\n                  value={part.name || ''}\n                  onChange={(e) => updatePart(index, 'name', e.target.value)}\n                  className=\"w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                  placeholder=\"Part name\"\n                />\n              </div>\n\n              <div className=\"grid grid-cols-2 gap-2\">\n                <div>\n                  <label className=\"block text-xs text-gray-600 mb-1\">Have</label>\n                  <input\n                    type=\"number\"\n                    min=\"0\"\n                    value={part.quantity || 0}\n                    onChange={(e) => updatePart(index, 'quantity', parseInt(e.target.value) || 0)}\n                    className=\"w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                  />\n                </div>\n                <div>\n                  <label className=\"block text-xs text-gray-600 mb-1\">Need</label>\n                  <input\n                    type=\"number\"\n                    min=\"1\"\n                    value={part.quantityNeeded || 1}\n                    onChange={(e) => updatePart(index, 'quantityNeeded', parseInt(e.target.value) || 1)}\n                    className=\"w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                  />\n                </div>\n              </div>\n\n              <div className=\"grid grid-cols-2 gap-2\">\n                <div>\n                  <input\n                    type=\"text\"\n                    value={part.supplier || ''}\n                    onChange={(e) => updatePart(index, 'supplier', e.target.value)}\n                    className=\"w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                    placeholder=\"Supplier\"\n                  />\n                </div>\n                <div>\n                  <input\n                    type=\"date\"\n                    value={part.expectedDelivery || ''}\n                    onChange={(e) => updatePart(index, 'expectedDelivery', e.target.value)}\n                    className=\"w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                  />\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n\n        {/* Notes */}\n        <div className=\"bg-white rounded-lg shadow-sm border p-4 space-y-4\">\n          <h2 className=\"text-lg font-semibold text-gray-900\">Notes</h2>\n          <textarea\n            value={notes}\n            onChange={(e) => setNotes(e.target.value)}\n            className=\"w-full p-3 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            rows={3}\n            placeholder=\"Additional notes about this order\"\n          />\n        </div>\n\n        {/* Submit Button */}\n        <button\n          type=\"submit\"\n          disabled={loading}\n          className=\"w-full bg-blue-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\n        >\n          {loading ? 'Creating Order...' : 'Create Order'}\n        </button>\n      </form>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AACA;AAEA;AACA;AARA;;;;;;;;AAUe,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,aAAa;IACb,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAwC;IAC/E,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,eAAe;IACf,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB;QACpD,MAAM;QACN,OAAO;QACP,OAAO;QACP,SAAS;QACT,SAAS;IACX;IAEA,cAAc;IACd,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB;QAClD;YACE,MAAM;YACN,UAAU;YACV,gBAAgB;YAChB,UAAU;YACV,kBAAkB;YAClB,MAAM;YACN,OAAO;QACT;KACD;IAED,MAAM,UAAU;QACd,SAAS;eAAI;YAAO;gBAClB,MAAM;gBACN,UAAU;gBACV,gBAAgB;gBAChB,UAAU;gBACV,kBAAkB;gBAClB,MAAM;gBACN,OAAO;YACT;SAAE;IACJ;IAEA,MAAM,aAAa,CAAC;QAClB,SAAS,MAAM,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;IACxC;IAEA,MAAM,aAAa,CAAC,OAAe,OAAmB;QACpD,MAAM,eAAe;eAAI;SAAM;QAC/B,YAAY,CAAC,MAAM,GAAG;YAAE,GAAG,YAAY,CAAC,MAAM;YAAE,CAAC,MAAM,EAAE;QAAM;QAC/D,SAAS;IACX;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,OAAO,IAAI,EAAE,QAAQ;YACzC,MAAM;YACN;QACF;QAEA,WAAW;QAEX,IAAI;YACF,MAAM,WAAkB;gBACtB,IAAI,CAAA,GAAA,0KAAA,CAAA,KAAM,AAAD;gBACT,OAAO,MAAM,IAAI;gBACjB,aAAa,YAAY,IAAI,MAAM;gBACnC,QAAQ;oBACN,IAAI,CAAA,GAAA,0KAAA,CAAA,KAAM,AAAD;oBACT,MAAM,OAAO,IAAI,CAAE,IAAI;oBACvB,OAAO,OAAO,KAAK,EAAE,UAAU;oBAC/B,OAAO,OAAO,KAAK,EAAE,UAAU;oBAC/B,SAAS,OAAO,OAAO,EAAE,UAAU;oBACnC,SAAS,OAAO,OAAO,EAAE,UAAU;gBACrC;gBACA,QAAQ;gBACR;gBACA,WAAW,IAAI,OAAO,WAAW;gBACjC,WAAW,IAAI,OAAO,WAAW;gBACjC,mBAAmB,qBAAqB;gBACxC,OAAO,MACJ,MAAM,CAAC,CAAA,OAAQ,KAAK,IAAI,EAAE,QAC1B,GAAG,CAAC,CAAA,OAAQ,CAAC;wBACZ,IAAI,CAAA,GAAA,0KAAA,CAAA,KAAM,AAAD;wBACT,MAAM,KAAK,IAAI,CAAE,IAAI;wBACrB,UAAU,KAAK,QAAQ,IAAI;wBAC3B,gBAAgB,KAAK,cAAc,IAAI;wBACvC,UAAU,KAAK,QAAQ,EAAE,UAAU;wBACnC,kBAAkB,KAAK,gBAAgB,IAAI;wBAC3C,MAAM,KAAK,IAAI,IAAI;wBACnB,OAAO,KAAK,KAAK,EAAE,UAAU;oBAC/B,CAAC;gBACH,aAAa,EAAE;gBACf,gBAAgB;gBAChB,OAAO,MAAM,IAAI,MAAM;YACzB;YAEA,CAAA,GAAA,qHAAA,CAAA,YAAS,AAAD,EAAE;YACV,OAAO,IAAI,CAAC,CAAC,QAAQ,EAAE,SAAS,EAAE,EAAE;QACtC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,4JAAA,CAAA,UAAI;wBAAC,MAAK;wBAAU,WAAU;kCAC7B,cAAA,8OAAC,gNAAA,CAAA,YAAS;4BAAC,MAAM;;;;;;;;;;;kCAEnB,8OAAC;wBAAG,WAAU;kCAAmC;;;;;;;;;;;;0BAGnD,8OAAC;gBAAK,UAAU;gBAAc,WAAU;;kCAEtC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAsC;;;;;;0CAEpD,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAGhE,8OAAC;wCACC,MAAK;wCACL,OAAO;wCACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;wCACxC,WAAU;wCACV,aAAY;wCACZ,QAAQ;;;;;;;;;;;;0CAIZ,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAGhE,8OAAC;wCACC,OAAO;wCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;wCAC9C,WAAU;wCACV,MAAM;wCACN,aAAY;;;;;;;;;;;;0CAIhB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,8OAAC;gDACC,OAAO;gDACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;gDAC3C,WAAU;;kEAEV,8OAAC;wDAAO,OAAM;kEAAM;;;;;;kEACpB,8OAAC;wDAAO,OAAM;kEAAS;;;;;;kEACvB,8OAAC;wDAAO,OAAM;kEAAO;;;;;;kEACrB,8OAAC;wDAAO,OAAM;kEAAS;;;;;;;;;;;;;;;;;;kDAI3B,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,8OAAC;gDACC,MAAK;gDACL,OAAO;gDACP,UAAU,CAAC,IAAM,qBAAqB,EAAE,MAAM,CAAC,KAAK;gDACpD,WAAU;;;;;;;;;;;;;;;;;;;;;;;;kCAOlB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAsC;;;;;;0CAEpD,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAGhE,8OAAC;wCACC,MAAK;wCACL,OAAO,OAAO,IAAI,IAAI;wCACtB,UAAU,CAAC,IAAM,UAAU;gDAAE,GAAG,MAAM;gDAAE,MAAM,EAAE,MAAM,CAAC,KAAK;4CAAC;wCAC7D,WAAU;wCACV,aAAY;wCACZ,QAAQ;;;;;;;;;;;;0CAIZ,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,8OAAC;gDACC,MAAK;gDACL,OAAO,OAAO,KAAK,IAAI;gDACvB,UAAU,CAAC,IAAM,UAAU;wDAAE,GAAG,MAAM;wDAAE,OAAO,EAAE,MAAM,CAAC,KAAK;oDAAC;gDAC9D,WAAU;gDACV,aAAY;;;;;;;;;;;;kDAIhB,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,8OAAC;gDACC,MAAK;gDACL,OAAO,OAAO,KAAK,IAAI;gDACvB,UAAU,CAAC,IAAM,UAAU;wDAAE,GAAG,MAAM;wDAAE,OAAO,EAAE,MAAM,CAAC,KAAK;oDAAC;gDAC9D,WAAU;gDACV,aAAY;;;;;;;;;;;;;;;;;;0CAKlB,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAGhE,8OAAC;wCACC,MAAK;wCACL,OAAO,OAAO,OAAO,IAAI;wCACzB,UAAU,CAAC,IAAM,UAAU;gDAAE,GAAG,MAAM;gDAAE,SAAS,EAAE,MAAM,CAAC,KAAK;4CAAC;wCAChE,WAAU;wCACV,aAAY;;;;;;;;;;;;0CAIhB,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAGhE,8OAAC;wCACC,OAAO,OAAO,OAAO,IAAI;wCACzB,UAAU,CAAC,IAAM,UAAU;gDAAE,GAAG,MAAM;gDAAE,SAAS,EAAE,MAAM,CAAC,KAAK;4CAAC;wCAChE,WAAU;wCACV,MAAM;wCACN,aAAY;;;;;;;;;;;;;;;;;;kCAMlB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAsC;;;;;;kDACpD,8OAAC;wCACC,MAAK;wCACL,SAAS;wCACT,WAAU;;0DAEV,8OAAC,kMAAA,CAAA,OAAI;gDAAC,MAAM;;;;;;4CAAM;;;;;;;;;;;;;4BAKrB,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC;oCAAgB,WAAU;;sDACzB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;;wDAAoC;wDAAM,QAAQ;;;;;;;gDACjE,MAAM,MAAM,GAAG,mBACd,8OAAC;oDACC,MAAK;oDACL,SAAS,IAAM,WAAW;oDAC1B,WAAU;8DAEV,cAAA,8OAAC,0MAAA,CAAA,SAAM;wDAAC,MAAM;;;;;;;;;;;;;;;;;sDAKpB,8OAAC;sDACC,cAAA,8OAAC;gDACC,MAAK;gDACL,OAAO,KAAK,IAAI,IAAI;gDACpB,UAAU,CAAC,IAAM,WAAW,OAAO,QAAQ,EAAE,MAAM,CAAC,KAAK;gDACzD,WAAU;gDACV,aAAY;;;;;;;;;;;sDAIhB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC;4DAAM,WAAU;sEAAmC;;;;;;sEACpD,8OAAC;4DACC,MAAK;4DACL,KAAI;4DACJ,OAAO,KAAK,QAAQ,IAAI;4DACxB,UAAU,CAAC,IAAM,WAAW,OAAO,YAAY,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;4DAC3E,WAAU;;;;;;;;;;;;8DAGd,8OAAC;;sEACC,8OAAC;4DAAM,WAAU;sEAAmC;;;;;;sEACpD,8OAAC;4DACC,MAAK;4DACL,KAAI;4DACJ,OAAO,KAAK,cAAc,IAAI;4DAC9B,UAAU,CAAC,IAAM,WAAW,OAAO,kBAAkB,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;4DACjF,WAAU;;;;;;;;;;;;;;;;;;sDAKhB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;8DACC,cAAA,8OAAC;wDACC,MAAK;wDACL,OAAO,KAAK,QAAQ,IAAI;wDACxB,UAAU,CAAC,IAAM,WAAW,OAAO,YAAY,EAAE,MAAM,CAAC,KAAK;wDAC7D,WAAU;wDACV,aAAY;;;;;;;;;;;8DAGhB,8OAAC;8DACC,cAAA,8OAAC;wDACC,MAAK;wDACL,OAAO,KAAK,gBAAgB,IAAI;wDAChC,UAAU,CAAC,IAAM,WAAW,OAAO,oBAAoB,EAAE,MAAM,CAAC,KAAK;wDACrE,WAAU;;;;;;;;;;;;;;;;;;mCA9DR;;;;;;;;;;;kCAuEd,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAsC;;;;;;0CACpD,8OAAC;gCACC,OAAO;gCACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;gCACxC,WAAU;gCACV,MAAM;gCACN,aAAY;;;;;;;;;;;;kCAKhB,8OAAC;wBACC,MAAK;wBACL,UAAU;wBACV,WAAU;kCAET,UAAU,sBAAsB;;;;;;;;;;;;;;;;;;AAK3C", "debugId": null}}, {"offset": {"line": 835, "column": 0}, "map": {"version": 3, "file": "arrow-left.js", "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Doug/New%20folder/time-tracker/node_modules/lucide-react/src/icons/arrow-left.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'm12 19-7-7 7-7', key: '1l729n' }],\n  ['path', { d: 'M19 12H5', key: 'x3x0zl' }],\n];\n\n/**\n * @component @name ArrowLeft\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMTIgMTktNy03IDctNyIgLz4KICA8cGF0aCBkPSJNMTkgMTJINSIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/arrow-left\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ArrowLeft = createLucideIcon('arrow-left', __iconNode);\n\nexport default ArrowLeft;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAkB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC/C;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CAC3C;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,SAAA,CAAY,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAc,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 881, "column": 0}, "map": {"version": 3, "file": "trash-2.js", "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Doug/New%20folder/time-tracker/node_modules/lucide-react/src/icons/trash-2.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M3 6h18', key: 'd0wm0j' }],\n  ['path', { d: 'M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6', key: '4alrt4' }],\n  ['path', { d: 'M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2', key: 'v07s0e' }],\n  ['line', { x1: '10', x2: '10', y1: '11', y2: '17', key: '1uufr5' }],\n  ['line', { x1: '14', x2: '14', y1: '11', y2: '17', key: 'xtxkd' }],\n];\n\n/**\n * @component @name Trash2\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMyA2aDE4IiAvPgogIDxwYXRoIGQ9Ik0xOSA2djE0YzAgMS0xIDItMiAySDdjLTEgMC0yLTEtMi0yVjYiIC8+CiAgPHBhdGggZD0iTTggNlY0YzAtMSAxLTIgMi0yaDRjMSAwIDIgMSAyIDJ2MiIgLz4KICA8bGluZSB4MT0iMTAiIHgyPSIxMCIgeTE9IjExIiB5Mj0iMTciIC8+CiAgPGxpbmUgeDE9IjE0IiB4Mj0iMTQiIHkxPSIxMSIgeTI9IjE3IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/trash-2\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Trash2 = createLucideIcon('trash-2', __iconNode);\n\nexport default Trash2;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACxC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAyC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACtE;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAsC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACnE;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAE;YAAA,CAAA,CAAA,EAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,EAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAClE;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAE;YAAA,CAAA,CAAA,EAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,EAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAS;KAAA;CACnE;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAS,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAW,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 954, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Doug/New%20folder/time-tracker/node_modules/uuid/dist/esm/native.js"], "sourcesContent": ["import { randomUUID } from 'crypto';\nexport default { randomUUID };\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,YAAA,qGAAA,CAAA,aAAU;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 968, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Doug/New%20folder/time-tracker/node_modules/uuid/dist/esm/rng.js"], "sourcesContent": ["import { randomFillSync } from 'crypto';\nconst rnds8Pool = new Uint8Array(256);\nlet poolPtr = rnds8Pool.length;\nexport default function rng() {\n    if (poolPtr > rnds8Pool.length - 16) {\n        randomFillSync(rnds8Pool);\n        poolPtr = 0;\n    }\n    return rnds8Pool.slice(poolPtr, (poolPtr += 16));\n}\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,YAAY,IAAI,WAAW;AACjC,IAAI,UAAU,UAAU,MAAM;AACf,SAAS;IACpB,IAAI,UAAU,UAAU,MAAM,GAAG,IAAI;QACjC,CAAA,GAAA,qGAAA,CAAA,iBAAc,AAAD,EAAE;QACf,UAAU;IACd;IACA,OAAO,UAAU,KAAK,CAAC,SAAU,WAAW;AAChD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 988, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Doug/New%20folder/time-tracker/node_modules/uuid/dist/esm/regex.js"], "sourcesContent": ["export default /^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-8][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000|ffffffff-ffff-ffff-ffff-ffffffffffff)$/i;\n"], "names": [], "mappings": ";;;uCAAe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 998, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Doug/New%20folder/time-tracker/node_modules/uuid/dist/esm/validate.js"], "sourcesContent": ["import REGEX from './regex.js';\nfunction validate(uuid) {\n    return typeof uuid === 'string' && REGEX.test(uuid);\n}\nexport default validate;\n"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,SAAS,IAAI;IAClB,OAAO,OAAO,SAAS,YAAY,4IAAA,CAAA,UAAK,CAAC,IAAI,CAAC;AAClD;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1013, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Doug/New%20folder/time-tracker/node_modules/uuid/dist/esm/stringify.js"], "sourcesContent": ["import validate from './validate.js';\nconst byteToHex = [];\nfor (let i = 0; i < 256; ++i) {\n    byteToHex.push((i + 0x100).toString(16).slice(1));\n}\nexport function unsafeStringify(arr, offset = 0) {\n    return (byteToHex[arr[offset + 0]] +\n        byteToHex[arr[offset + 1]] +\n        byteToHex[arr[offset + 2]] +\n        byteToHex[arr[offset + 3]] +\n        '-' +\n        byteToHex[arr[offset + 4]] +\n        byteToHex[arr[offset + 5]] +\n        '-' +\n        byteToHex[arr[offset + 6]] +\n        byteToHex[arr[offset + 7]] +\n        '-' +\n        byteToHex[arr[offset + 8]] +\n        byteToHex[arr[offset + 9]] +\n        '-' +\n        byteToHex[arr[offset + 10]] +\n        byteToHex[arr[offset + 11]] +\n        byteToHex[arr[offset + 12]] +\n        byteToHex[arr[offset + 13]] +\n        byteToHex[arr[offset + 14]] +\n        byteToHex[arr[offset + 15]]).toLowerCase();\n}\nfunction stringify(arr, offset = 0) {\n    const uuid = unsafeStringify(arr, offset);\n    if (!validate(uuid)) {\n        throw TypeError('Stringified UUID is invalid');\n    }\n    return uuid;\n}\nexport default stringify;\n"], "names": [], "mappings": ";;;;AAAA;;AACA,MAAM,YAAY,EAAE;AACpB,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,EAAE,EAAG;IAC1B,UAAU,IAAI,CAAC,CAAC,IAAI,KAAK,EAAE,QAAQ,CAAC,IAAI,KAAK,CAAC;AAClD;AACO,SAAS,gBAAgB,GAAG,EAAE,SAAS,CAAC;IAC3C,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAC9B,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAC1B,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAC1B,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAC1B,MACA,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAC1B,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAC1B,MACA,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAC1B,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAC1B,MACA,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAC1B,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAC1B,MACA,SAAS,CAAC,GAAG,CAAC,SAAS,GAAG,CAAC,GAC3B,SAAS,CAAC,GAAG,CAAC,SAAS,GAAG,CAAC,GAC3B,SAAS,CAAC,GAAG,CAAC,SAAS,GAAG,CAAC,GAC3B,SAAS,CAAC,GAAG,CAAC,SAAS,GAAG,CAAC,GAC3B,SAAS,CAAC,GAAG,CAAC,SAAS,GAAG,CAAC,GAC3B,SAAS,CAAC,GAAG,CAAC,SAAS,GAAG,CAAC,EAAE,WAAW;AAChD;AACA,SAAS,UAAU,GAAG,EAAE,SAAS,CAAC;IAC9B,MAAM,OAAO,gBAAgB,KAAK;IAClC,IAAI,CAAC,CAAA,GAAA,+IAAA,CAAA,UAAQ,AAAD,EAAE,OAAO;QACjB,MAAM,UAAU;IACpB;IACA,OAAO;AACX;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1040, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/Doug/New%20folder/time-tracker/node_modules/uuid/dist/esm/v4.js"], "sourcesContent": ["import native from './native.js';\nimport rng from './rng.js';\nimport { unsafeStringify } from './stringify.js';\nfunction v4(options, buf, offset) {\n    if (native.randomUUID && !buf && !options) {\n        return native.randomUUID();\n    }\n    options = options || {};\n    const rnds = options.random ?? options.rng?.() ?? rng();\n    if (rnds.length < 16) {\n        throw new Error('Random bytes length must be >= 16');\n    }\n    rnds[6] = (rnds[6] & 0x0f) | 0x40;\n    rnds[8] = (rnds[8] & 0x3f) | 0x80;\n    if (buf) {\n        offset = offset || 0;\n        if (offset < 0 || offset + 16 > buf.length) {\n            throw new RangeError(`UUID byte range ${offset}:${offset + 15} is out of buffer bounds`);\n        }\n        for (let i = 0; i < 16; ++i) {\n            buf[offset + i] = rnds[i];\n        }\n        return buf;\n    }\n    return unsafeStringify(rnds);\n}\nexport default v4;\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AACA,SAAS,GAAG,OAAO,EAAE,GAAG,EAAE,MAAM;IAC5B,IAAI,6IAAA,CAAA,UAAM,CAAC,UAAU,IAAI,CAAC,OAAO,CAAC,SAAS;QACvC,OAAO,6IAAA,CAAA,UAAM,CAAC,UAAU;IAC5B;IACA,UAAU,WAAW,CAAC;IACtB,MAAM,OAAO,QAAQ,MAAM,IAAI,QAAQ,GAAG,QAAQ,CAAA,GAAA,0IAAA,CAAA,UAAG,AAAD;IACpD,IAAI,KAAK,MAAM,GAAG,IAAI;QAClB,MAAM,IAAI,MAAM;IACpB;IACA,IAAI,CAAC,EAAE,GAAG,AAAC,IAAI,CAAC,EAAE,GAAG,OAAQ;IAC7B,IAAI,CAAC,EAAE,GAAG,AAAC,IAAI,CAAC,EAAE,GAAG,OAAQ;IAC7B,IAAI,KAAK;QACL,SAAS,UAAU;QACnB,IAAI,SAAS,KAAK,SAAS,KAAK,IAAI,MAAM,EAAE;YACxC,MAAM,IAAI,WAAW,CAAC,gBAAgB,EAAE,OAAO,CAAC,EAAE,SAAS,GAAG,wBAAwB,CAAC;QAC3F;QACA,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,EAAE,EAAG;YACzB,GAAG,CAAC,SAAS,EAAE,GAAG,IAAI,CAAC,EAAE;QAC7B;QACA,OAAO;IACX;IACA,OAAO,CAAA,GAAA,gJAAA,CAAA,kBAAe,AAAD,EAAE;AAC3B;uCACe", "ignoreList": [0], "debugId": null}}]}